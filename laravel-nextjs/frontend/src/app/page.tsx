'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { apiClient, type Company, type Category, type Coupon } from '@/lib/api';
import { cn, formatRating } from '@/lib/utils';
import {
  ClockIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

export default function Home() {
  const [topCompanies, setTopCompanies] = useState<Company[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [activeCoupons, setActiveCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [companiesData, categoriesData, couponsData] = await Promise.all([
          apiClient.getTopCompanies(),
          apiClient.getCategories(),
          apiClient.getActiveCoupons()
        ]);

        setTopCompanies(companiesData);
        setCategories(categoriesData);
        setActiveCoupons(couponsData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Żyrardów Poleca
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Odkryj najlepsze firmy i usługi w Żyrardowie
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/firmy"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Przeglądaj Firmy
              </Link>
              <Link
                href="/kategorie"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Zobacz Kategorie
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* TOP Companies Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              TOP Firmy w Żyrardowie
            </h2>
            <p className="text-lg text-gray-600">
              Najlepiej oceniane i najpopularniejsze firmy w naszym mieście
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {topCompanies.map((company) => (
              <Link
                key={company.id}
                href={`/firmy/${company.slug}`}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {company.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-2">
                      {company.category?.name}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <StarIconSolid className="h-5 w-5 text-yellow-400" />
                    <span className="ml-1 text-sm font-medium text-gray-900">
                      {formatRating(company.rating)}
                    </span>
                  </div>
                </div>

                <p className="text-gray-600 mb-4 line-clamp-2">
                  {company.short_description}
                </p>

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    <span className={cn(
                      company.is_open_now ? 'text-green-600' : 'text-red-600'
                    )}>
                      {company.is_open_now ? 'Otwarte' : 'Zamknięte'}
                    </span>
                  </div>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                    TOP
                  </span>
                </div>
              </Link>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/firmy"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Zobacz wszystkie firmy
            </Link>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Kategorie Firm
            </h2>
            <p className="text-lg text-gray-600">
              Znajdź firmy według kategorii
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {categories.slice(0, 8).map((category) => (
              <Link
                key={category.id}
                href={`/kategorie/${category.slug}`}
                className="group p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-center"
              >
                <div
                  className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-2xl"
                  style={{ backgroundColor: category.color + '20' }}
                >
                  {category.icon}
                </div>
                <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-600">
                  {category.companies_count} {category.companies_count === 1 ? 'firma' : 'firm'}
                </p>
              </Link>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/kategorie"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              Zobacz wszystkie kategorie
            </Link>
          </div>
        </div>
      </section>

      {/* Active Coupons Section */}
      {activeCoupons.length > 0 && (
        <section className="py-16 bg-gradient-to-r from-green-50 to-blue-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Aktualne Kupony Rabatowe
              </h2>
              <p className="text-lg text-gray-600">
                Skorzystaj z ekskluzywnych ofert od lokalnych firm
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activeCoupons.slice(0, 6).map((coupon) => (
                <div
                  key={coupon.id}
                  className="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {coupon.title}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {coupon.company?.name}
                      </p>
                    </div>
                    <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                      {coupon.discount}
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4 text-sm">
                    {coupon.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="bg-gray-100 px-3 py-1 rounded font-mono text-sm">
                      {coupon.code}
                    </div>
                    <div className="text-xs text-gray-500">
                      Ważny do: {new Date(coupon.valid_to).toLocaleDateString('pl-PL')}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <Link
                href="/kupony"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors"
              >
                <TagIcon className="h-5 w-5 mr-2" />
                Zobacz wszystkie kupony
              </Link>
            </div>
          </div>
        </section>
      )}
    </div>
  );
}
