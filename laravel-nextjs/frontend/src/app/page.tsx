'use client'

import { useState, useEffect } from 'react'
import { Company, Category, companiesApi, categoriesApi } from '@/lib/api'
import CompanyCard from '@/components/CompanyCard'
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import Link from 'next/link'

export default function HomePage() {
  const [topCompanies, setTopCompanies] = useState<Company[]>([])
  const [featuredCompanies, setFeaturedCompanies] = useState<Company[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [topResponse, featuredResponse, categoriesResponse] = await Promise.all([
          companiesApi.getTop(),
          companiesApi.getFeatured(6),
          categoriesApi.getAll(),
        ])

        setTopCompanies(topResponse.data.data)
        setFeaturedCompanies(featuredResponse.data.data)
        setCategories(categoriesResponse.data.data)
      } catch (error) {
        console.error('Failed to fetch data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/firmy?search=${encodeURIComponent(searchQuery.trim())}`
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 animate-fade-in">
              Żyrardów <span className="text-yellow-400">Poleca</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Odkryj najlepsze firmy i usługi w Żyrardowie
            </p>
            
            {/* Wyszukiwarka */}
            <form onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Szukaj firm, usług, kategorii..."
                  className="w-full px-6 py-4 text-lg rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-blue-300"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full transition-colors"
                >
                  <MagnifyingGlassIcon className="w-6 h-6" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* TOP Firmy */}
      {topCompanies.length > 0 && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                TOP Firmy w Żyrardowie
              </h2>
              <p className="text-xl text-gray-600">
                Najlepsze i najczęściej wybierane firmy przez mieszkańców
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {topCompanies.map((company, index) => (
                <CompanyCard 
                  key={company.id} 
                  company={company} 
                  priority={index < 3}
                />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Kategorie */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Przeglądaj Kategorie
            </h2>
            <p className="text-xl text-gray-600">
              Znajdź firmy w interesującej Cię branży
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/kategorie/${category.slug}`}
                className="group bg-white rounded-xl p-6 text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
              >
                <div 
                  className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center text-white text-2xl group-hover:scale-110 transition-transform"
                  style={{ backgroundColor: category.color }}
                >
                  <i className={category.icon}></i>
                </div>
                <h3 className="font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-500">
                  {category.active_companies_count} firm
                </p>
              </Link>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link
              href="/kategorie"
              className="inline-flex items-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-full transition-colors"
            >
              Zobacz wszystkie kategorie
            </Link>
          </div>
        </div>
      </section>

      {/* Polecane Firmy */}
      {featuredCompanies.length > 0 && (
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Polecane Firmy
              </h2>
              <p className="text-xl text-gray-600">
                Firmy z najwyższymi ocenami i największą liczbą wyświetleń
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredCompanies.map((company) => (
                <CompanyCard 
                  key={company.id} 
                  company={company}
                  showCategory={true}
                />
              ))}
            </div>
            
            <div className="text-center mt-12">
              <Link
                href="/firmy"
                className="inline-flex items-center px-8 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-full transition-colors"
              >
                Zobacz wszystkie firmy
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">
            Masz firmę w Żyrardowie?
          </h2>
          <p className="text-xl mb-8 text-green-100">
            Dołącz do naszego katalogu i zyskaj nowych klientów
          </p>
          <Link
            href="/dodaj-firme"
            className="inline-flex items-center px-8 py-4 bg-white text-green-600 font-bold rounded-full hover:bg-gray-100 transition-colors text-lg"
          >
            Dodaj swoją firmę
          </Link>
        </div>
      </section>
    </div>
  )
}
