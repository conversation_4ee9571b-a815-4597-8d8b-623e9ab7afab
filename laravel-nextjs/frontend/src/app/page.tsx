'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

// Temporary types until we fix imports
interface Company {
  id: number;
  name: string;
  slug: string;
  short_description?: string;
  rating: number;
  category?: string;
  is_open_now: boolean;
  logo?: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  color: string;
  companies_count: number;
}

interface Coupon {
  id: number;
  title: string;
  description: string;
  code: string;
  discount: string;
  valid_to: string;
  company?: {
    name: string;
  };
}

export default function Home() {
  const [topCompanies, setTopCompanies] = useState<Company[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [activeCoupons, setActiveCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    // Temporary mock data until we fix API imports
    const mockCategories: Category[] = [
      { id: 1, name: 'Gastronomia', slug: 'gastronomia', icon: 'fas fa-utensils', color: '#e74c3c', companies_count: 12 },
      { id: 2, name: 'Zdrowie i uroda', slug: 'zdrowie-uroda', icon: 'fas fa-heartbeat', color: '#27ae60', companies_count: 8 },
      { id: 3, name: 'Uroda', slug: 'uroda', icon: 'fas fa-cut', color: '#9b59b6', companies_count: 6 },
      { id: 4, name: 'Sport i rekreacja', slug: 'sport-rekreacja', icon: 'fas fa-dumbbell', color: '#3498db', companies_count: 4 },
      { id: 5, name: 'Motoryzacja', slug: 'motoryzacja', icon: 'fas fa-car', color: '#f39c12', companies_count: 7 },
      { id: 6, name: 'Usługi', slug: 'uslugi', icon: 'fas fa-tools', color: '#34495e', companies_count: 15 },
      { id: 7, name: 'Zakupy', slug: 'zakupy', icon: 'fas fa-shopping-bag', color: '#e67e22', companies_count: 9 },
      { id: 8, name: 'Edukacja', slug: 'edukacja', icon: 'fas fa-graduation-cap', color: '#2c3e50', companies_count: 3 },
    ];

    const mockCompanies: Company[] = [
      { id: 1, name: 'Restauracja Pod Lipami', slug: 'restauracja-pod-lipami', short_description: 'Tradycyjna kuchnia polska w sercu Żyrardowa', rating: 4.8, category: 'Gastronomia', is_open_now: true },
      { id: 2, name: 'Salon Piękności Venus', slug: 'salon-pieknosci-venus', short_description: 'Profesjonalne usługi kosmetyczne i fryzjerskie', rating: 4.6, category: 'Uroda', is_open_now: false },
      { id: 3, name: 'Warsztat Samochodowy Auto-Serwis', slug: 'warsztat-auto-serwis', short_description: 'Kompleksowe naprawy i serwis pojazdów', rating: 4.7, category: 'Motoryzacja', is_open_now: true },
    ];

    const mockCoupons: Coupon[] = [
      { id: 1, title: '20% zniżki na obiad', description: 'Rabat na wszystkie dania główne', code: 'OBIAD20', discount: '20%', valid_to: '2025-12-31', company: { name: 'Restauracja Pod Lipami' } },
      { id: 2, title: 'Darmowa konsultacja', description: 'Bezpłatna konsultacja kosmetyczna', code: 'KONSULT', discount: '100%', valid_to: '2025-06-30', company: { name: 'Salon Piękności Venus' } },
    ];

    setCategories(mockCategories);
    setTopCompanies(mockCompanies);
    setActiveCoupons(mockCoupons);
    setLoading(false);
  }, []);

  // Auto-slide functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % 3);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  const slides = [
    {
      bg: '/images/zyrardow-slider1.jpg',
      title: 'Odkryj TOP firmy w Żyrardowie 2025',
      subtitle: '🏆 Portal lokalny #1 w Żyrardowie - najlepsze firmy, kupony rabatowe i usługi w mieście Filip de Girard',
      primaryBtn: { text: 'Katalog firm Żyrardów', href: '#categories' },
      secondaryBtn: { text: 'Dla przedsiębiorców', href: '/dla-biznesu' }
    },
    {
      bg: '/images/zyrardow-slider2.jpg',
      title: 'Historia Żyrardowa - Filip de Girard i Pomnik Historii',
      subtitle: '🏛️ Odkryj unikalne dziedzictwo przemysłowe - osada fabryczna, Muzeum Lniarstwa i zabytki XIX wieku',
      primaryBtn: { text: 'Historia Żyrardowa', href: '/historia' },
      secondaryBtn: { text: 'Zabytki przemysłowe', href: '/zabytki' }
    },
    {
      bg: '/images/zyrardow-slider3.jpg',
      title: 'Kupony rabatowe Żyrardów - oszczędzaj lokalnie!',
      subtitle: '💰 Ekskluzywne rabaty w restauracjach, salonach i sklepach Żyrardowa - tylko dla mieszkańców!',
      primaryBtn: { text: 'Kupony Żyrardów', href: '/kupony' },
      secondaryBtn: { text: 'Promocje lokalne', href: '/oferty' }
    }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <header className="site-header">
        <div className="container">
          <nav className="main-nav">
            <div className="logo">
              <Link href="/">
                <Image
                  src="/images/logo-zyrardow-poleca.png"
                  alt="Żyrardów.poleca.to"
                  width={200}
                  height={60}
                />
              </Link>
            </div>
            <ul className="nav-links">
              <li><Link href="#categories-section">Polecane firmy i miejsca</Link></li>
              <li className="has-submenu">
                <Link href="/o-miescie">O mieście</Link>
                <ul className="submenu">
                  <li><Link href="/o-miescie">O Żyrardowie</Link></li>
                  <li><Link href="/historia">Historia</Link></li>
                  <li><Link href="/zabytki">Zabytki</Link></li>
                  <li><Link href="/powiat">Powiat żyrardowski</Link></li>
                </ul>
              </li>
              <li className="has-submenu">
                <Link href="/atrakcje">Atrakcje w Żyrardowie</Link>
                <ul className="submenu">
                  <li><Link href="/atrakcje">Miejsca warte odwiedzenia</Link></li>
                  <li><Link href="/osada-fabryczna">Osada fabryczna</Link></li>
                  <li><Link href="/park-dittricha">Park Dittricha</Link></li>
                  <li><Link href="/muzeum-lniarstwa">Muzeum Lniarstwa</Link></li>
                  <li><Link href="/kosciol-mb-pocieszenia">Kościół MB Pocieszenia</Link></li>
                  <li><Link href="/centrum-kultury">Centrum Kultury</Link></li>
                  <li><Link href="/aquapark">Aquapark</Link></li>
                  <li><Link href="/kultura-sport">Kultura i sport</Link></li>
                </ul>
              </li>
              <li><Link href="/kontakt">Kontakt</Link></li>
            </ul>
            <div className="nav-actions">
              <Link href="https://facebook.com/zyrardow.poleca.to" className="social-icon" target="_blank" rel="noopener noreferrer">
                <i className="fab fa-facebook-f"></i>
              </Link>
              <button className="mobile-menu-btn">
                <span></span>
                <span></span>
                <span></span>
              </button>
            </div>
          </nav>
          <div className="search-container">
            <form className="search-form">
              <input type="text" placeholder="Szukaj w serwisie..." />
              <button type="submit"><i className="fas fa-search"></i></button>
            </form>
          </div>
        </div>
      </header>

      <main>
        {/* Hero Section */}
        <section className="hero">
          <div className="hero-slider">
            {slides.map((slide, index) => (
              <div
                key={index}
                className={`hero-slide ${index === currentSlide ? 'active' : ''}`}
              >
                <div
                  className="hero-slide-bg"
                  style={{ backgroundImage: `url('${slide.bg}')` }}
                ></div>
                <div className="hero-overlay"></div>
                <div className="container">
                  <div className="hero-content">
                    <h1>{slide.title}</h1>
                    <p className="lead">{slide.subtitle}</p>
                    <div className="hero-buttons">
                      <Link href={slide.primaryBtn.href} className="btn btn-primary">
                        {slide.primaryBtn.text}
                      </Link>
                      <Link href={slide.secondaryBtn.href} className="btn btn-outline">
                        {slide.secondaryBtn.text}
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="hero-controls">
            <button
              className="hero-control prev"
              onClick={() => setCurrentSlide((prev) => (prev - 1 + 3) % 3)}
            >
              <i className="fas fa-chevron-left"></i>
            </button>
            <div className="hero-dots">
              {slides.map((_, index) => (
                <span
                  key={index}
                  className={`hero-dot ${index === currentSlide ? 'active' : ''}`}
                  onClick={() => setCurrentSlide(index)}
                ></span>
              ))}
            </div>
            <button
              className="hero-control next"
              onClick={() => setCurrentSlide((prev) => (prev + 1) % 3)}
            >
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>
        </section>

        {/* Categories Section */}
        <section id="categories-section" className="categories-section">
          <div className="container">
            <div className="section-header">
              <h2>Polecane firmy i miejsca w Żyrardowie</h2>
              <p>Odkryj najlepsze lokalne firmy, usługi i atrakcje w naszym mieście</p>
            </div>

            <div className="categories-grid">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={`/kategorie/${category.slug}`}
                  className="category-card"
                >
                  <div className="category-icon" style={{ backgroundColor: category.color }}>
                    <i className={category.icon}></i>
                  </div>
                  <h3>{category.name}</h3>
                  <p>{category.companies_count} {category.companies_count === 1 ? 'firma' : 'firm'}</p>
                  <span className="category-arrow">→</span>
                </Link>
              ))}
            </div>
          </div>
        </section>

        {/* TOP Companies Section */}
        {topCompanies.length > 0 && (
          <section className="top-companies-section">
            <div className="container">
              <div className="section-header">
                <h2>TOP firmy w Żyrardowie</h2>
                <p>Najlepiej oceniane i najpopularniejsze firmy w naszym mieście</p>
              </div>

              <div className="companies-grid">
                {topCompanies.map((company) => (
                  <div key={company.id} className="company-card">
                    <div className="company-header">
                      <div className="company-logo">
                        {company.logo ? (
                          <Image
                            src={company.logo}
                            alt={company.name}
                            width={60}
                            height={60}
                          />
                        ) : (
                          <div className="company-logo-placeholder">
                            {company.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div className="company-info">
                        <h3>{company.name}</h3>
                        <p className="company-category">{company.category}</p>
                        <div className="company-rating">
                          <div className="stars">
                            {[...Array(5)].map((_, i) => (
                              <i
                                key={i}
                                className={`fas fa-star ${i < Math.floor(company.rating) ? 'active' : ''}`}
                              ></i>
                            ))}
                          </div>
                          <span className="rating-value">{company.rating}</span>
                        </div>
                      </div>
                      <div className="top-badge">TOP</div>
                    </div>

                    <p className="company-description">{company.short_description}</p>

                    <div className="company-actions">
                      <Link href={`/firmy/${company.slug}`} className="btn btn-primary btn-sm">
                        Zobacz szczegóły
                      </Link>
                      <button className="btn btn-outline btn-sm">
                        <i className="fas fa-heart"></i>
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="section-footer">
                <Link href="/firmy" className="btn btn-primary">
                  Zobacz wszystkie firmy
                </Link>
              </div>
            </div>
          </section>
        )}

        {/* Active Coupons Section */}
        {activeCoupons.length > 0 && (
          <section className="coupons-section">
            <div className="container">
              <div className="section-header">
                <h2>Aktualne kupony rabatowe</h2>
                <p>Skorzystaj z ekskluzywnych ofert od lokalnych firm</p>
              </div>

              <div className="coupons-grid">
                {activeCoupons.slice(0, 6).map((coupon) => (
                  <div key={coupon.id} className="coupon-card">
                    <div className="coupon-header">
                      <div className="coupon-discount">{coupon.discount}</div>
                      <div className="coupon-company">{coupon.company?.name}</div>
                    </div>

                    <h3 className="coupon-title">{coupon.title}</h3>
                    <p className="coupon-description">{coupon.description}</p>

                    <div className="coupon-footer">
                      <div className="coupon-code">
                        <span>Kod:</span>
                        <strong>{coupon.code}</strong>
                      </div>
                      <div className="coupon-validity">
                        Ważny do: {new Date(coupon.valid_to).toLocaleDateString('pl-PL')}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="section-footer">
                <Link href="/kupony" className="btn btn-primary">
                  Zobacz wszystkie kupony
                </Link>
              </div>
            </div>
          </section>
        )}
      </main>

      {/* Footer */}
      <footer className="site-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-section">
              <div className="footer-logo">
                <Image
                  src="/images/logo-zyrardow-poleca.png"
                  alt="Żyrardów.poleca.to"
                  width={200}
                  height={60}
                />
              </div>
              <p>Portal lokalny Żyrardowa - odkryj najlepsze firmy, usługi i atrakcje w mieście Filip de Girard.</p>
              <div className="social-links">
                <Link href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-facebook-f"></i>
                </Link>
                <Link href="https://instagram.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">
                  <i className="fab fa-instagram"></i>
                </Link>
              </div>
            </div>

            <div className="footer-section">
              <h4>Dla firm</h4>
              <ul>
                <li><Link href="/dla-biznesu">Dodaj firmę</Link></li>
                <li><Link href="/cennik">Cennik</Link></li>
                <li><Link href="/kontakt">Kontakt</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>O Żyrardowie</h4>
              <ul>
                <li><Link href="/o-miescie">O mieście</Link></li>
                <li><Link href="/historia">Historia</Link></li>
                <li><Link href="/atrakcje">Atrakcje</Link></li>
                <li><Link href="/powiat">Powiat</Link></li>
              </ul>
            </div>

            <div className="footer-section">
              <h4>Kontakt</h4>
              <ul>
                <li><i className="fas fa-envelope"></i> <EMAIL></li>
                <li><i className="fas fa-phone"></i> +48 123 456 789</li>
                <li><i className="fas fa-map-marker-alt"></i> Żyrardów, Polska</li>
              </ul>
            </div>
          </div>

          <div className="footer-bottom">
            <p>&copy; 2025 Żyrardów.poleca.to - Wszystkie prawa zastrzeżone</p>
            <div className="footer-links">
              <Link href="/regulamin">Regulamin</Link>
              <Link href="/polityka-prywatnosci">Polityka prywatności</Link>
              <Link href="/cookies">Cookies</Link>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
}
