import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Żyrardów.poleca.to - Portal lokalny Żyrardowa",
  description: "Odk<PERSON><PERSON> najlepsze firmy, usługi i atrakcje w Żyrardowie. Portal lokalny z katalogiem firm, kuponami rabatowymi i informacjami o mieście Filip de Girard.",
  other: {
    'font-awesome': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pl">
      <body className="antialiased">
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
        {children}
      </body>
    </html>
  );
}
