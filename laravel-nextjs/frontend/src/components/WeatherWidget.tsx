'use client';

import { useEffect, useState } from 'react';

interface WeatherData {
  temperature: number;
  description: string;
  icon: string;
  humidity: number;
  windSpeed: number;
  pressure: number;
  feelsLike: number;
}

export default function WeatherWidget() {
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchWeather = async () => {
      try {
        const API_KEY = '1bbd7c97be5cf33d539dfbb02ea63e99';
        const LAT = 52.0500;
        const LON = 20.4500;
        
        const response = await fetch(
          `https://api.openweathermap.org/data/2.5/weather?lat=${LAT}&lon=${LON}&appid=${API_KEY}&units=metric&lang=pl`
        );
        
        if (!response.ok) {
          throw new Error('Błąd pobierania danych pogodowych');
        }
        
        const data = await response.json();
        
        setWeather({
          temperature: Math.round(data.main.temp),
          description: data.weather[0].description,
          icon: data.weather[0].icon,
          humidity: data.main.humidity,
          windSpeed: Math.round(data.wind.speed * 3.6), // m/s to km/h
          pressure: data.main.pressure,
          feelsLike: Math.round(data.main.feels_like)
        });
      } catch (err) {
        setError('Nie udało się pobrać danych pogodowych');
        console.error('Weather API error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchWeather();
    
    // Odświeżaj co 10 minut
    const interval = setInterval(fetchWeather, 10 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="weather-widget loading">
        <div className="weather-icon">
          <i className="fas fa-spinner fa-spin"></i>
        </div>
        <div className="weather-info">
          <div className="weather-temp">--°C</div>
          <div className="weather-desc">Ładowanie...</div>
        </div>
      </div>
    );
  }

  if (error || !weather) {
    return (
      <div className="weather-widget error">
        <div className="weather-icon">
          <i className="fas fa-exclamation-triangle"></i>
        </div>
        <div className="weather-info">
          <div className="weather-temp">--°C</div>
          <div className="weather-desc">Błąd pogody</div>
        </div>
      </div>
    );
  }

  return (
    <div className="weather-widget">
      <div className="weather-header">
        <i className="fas fa-map-marker-alt"></i>
        <span>Żyrardów</span>
      </div>
      
      <div className="weather-main">
        <div className="weather-icon">
          <img 
            src={`https://openweathermap.org/img/wn/${weather.icon}@2x.png`}
            alt={weather.description}
            width={50}
            height={50}
          />
        </div>
        <div className="weather-info">
          <div className="weather-temp">{weather.temperature}°C</div>
          <div className="weather-desc">{weather.description}</div>
        </div>
      </div>
      
      <div className="weather-details">
        <div className="weather-detail">
          <i className="fas fa-thermometer-half"></i>
          <span>Odczuwalna: {weather.feelsLike}°C</span>
        </div>
        <div className="weather-detail">
          <i className="fas fa-tint"></i>
          <span>Wilgotność: {weather.humidity}%</span>
        </div>
        <div className="weather-detail">
          <i className="fas fa-wind"></i>
          <span>Wiatr: {weather.windSpeed} km/h</span>
        </div>
        <div className="weather-detail">
          <i className="fas fa-eye"></i>
          <span>Ciśnienie: {weather.pressure} hPa</span>
        </div>
      </div>
      
      <div className="weather-footer">
        <small>Ostatnia aktualizacja: {new Date().toLocaleTimeString('pl-PL')}</small>
      </div>
    </div>
  );
}
