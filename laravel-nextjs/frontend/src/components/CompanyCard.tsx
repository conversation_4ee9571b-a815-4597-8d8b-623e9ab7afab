'use client'

import { Company, companiesApi } from '@/lib/api'
import { MapPinIcon, PhoneIcon, GlobeAltIcon } from '@heroicons/react/24/outline'
import { StarIcon } from '@heroicons/react/24/solid'
import Image from 'next/image'
import Link from 'next/link'

interface CompanyCardProps {
  company: Company
  showCategory?: boolean
  priority?: boolean
}

export default function CompanyCard({ 
  company, 
  showCategory = true, 
  priority = false 
}: CompanyCardProps) {
  const handleClick = async () => {
    try {
      await companiesApi.recordClick(company.id)
    } catch (error) {
      console.error('Failed to record click:', error)
    }
  }

  const handleWebsiteClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    handleClick()
    if (company.website) {
      window.open(company.website, '_blank', 'noopener,noreferrer')
    }
  }

  const handlePhoneClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    handleClick()
  }

  return (
    <div className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group">
      {/* Header z logo i pozycją TOP */}
      <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200">
        {company.logo.thumb ? (
          <Image
            src={company.logo.thumb}
            alt={`Logo ${company.name}`}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            priority={priority}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
              <span className="text-2xl font-bold text-gray-600">
                {company.name.charAt(0)}
              </span>
            </div>
          </div>
        )}
        
        {/* Badge TOP */}
        {company.is_top && (
          <div className="absolute top-3 left-3 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
            <StarIcon className="w-3 h-3" />
            TOP {company.top_position}
          </div>
        )}

        {/* Badge kategorii */}
        {showCategory && (
          <div 
            className="absolute top-3 right-3 px-2 py-1 rounded-full text-xs font-medium text-white"
            style={{ backgroundColor: company.category.color }}
          >
            {company.category.name}
          </div>
        )}
      </div>

      {/* Treść */}
      <div className="p-6">
        <Link 
          href={`/firmy/${company.slug}`}
          onClick={handleClick}
          className="block group-hover:text-blue-600 transition-colors"
        >
          <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
            {company.name}
          </h3>
        </Link>

        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {company.description}
        </p>

        {/* Adres */}
        <div className="flex items-start gap-2 text-sm text-gray-500 mb-3">
          <MapPinIcon className="w-4 h-4 mt-0.5 flex-shrink-0" />
          <span className="line-clamp-2">{company.full_address}</span>
        </div>

        {/* Kontakt */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {company.phone && (
              <a
                href={`tel:${company.phone}`}
                onClick={handlePhoneClick}
                className="flex items-center gap-1 text-blue-600 hover:text-blue-800 text-sm transition-colors"
              >
                <PhoneIcon className="w-4 h-4" />
                <span className="hidden sm:inline">Zadzwoń</span>
              </a>
            )}
            
            {company.website && (
              <button
                onClick={handleWebsiteClick}
                className="flex items-center gap-1 text-green-600 hover:text-green-800 text-sm transition-colors"
              >
                <GlobeAltIcon className="w-4 h-4" />
                <span className="hidden sm:inline">Strona</span>
              </button>
            )}
          </div>

          {/* Statystyki */}
          <div className="text-xs text-gray-400">
            {company.views_count} wyświetleń
          </div>
        </div>

        {/* Kupony */}
        {company.coupons && company.coupons.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-red-600">
                {company.coupons.length} aktywn{company.coupons.length === 1 ? 'y kupon' : 'e kupony'}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
