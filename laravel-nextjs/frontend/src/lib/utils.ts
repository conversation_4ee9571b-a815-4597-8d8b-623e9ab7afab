import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatRating(rating: number): string {
  return rating.toFixed(1);
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('pl-PL', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function formatPhoneNumber(phone: string): string {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format as +48 XX XXX XX XX
  if (cleaned.length === 9) {
    return `+48 ${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 7)} ${cleaned.slice(7)}`;
  }
  
  return phone;
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^(\+48\s?)?[\d\s\-\(\)]{9,}$/;
  return phoneRegex.test(phone);
}

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[ąćęłńóśźż]/g, (match) => {
      const map: { [key: string]: string } = {
        'ą': 'a', 'ć': 'c', 'ę': 'e', 'ł': 'l', 'ń': 'n',
        'ó': 'o', 'ś': 's', 'ź': 'z', 'ż': 'z'
      };
      return map[match] || match;
    })
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

export function getBusinessHoursStatus(openingHours?: Array<{ day: string; hours: string }>): {
  isOpen: boolean;
  nextOpenTime?: string;
  todayHours?: string;
} {
  if (!openingHours || openingHours.length === 0) {
    return { isOpen: false };
  }

  const now = new Date();
  const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const currentTime = now.getHours() * 60 + now.getMinutes(); // minutes since midnight

  // Find today's hours
  const todaySchedule = openingHours.find(
    schedule => schedule.day.toLowerCase() === currentDay
  );

  if (!todaySchedule || todaySchedule.hours === 'closed') {
    // Find next open day
    const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const currentDayIndex = daysOfWeek.indexOf(currentDay);
    
    for (let i = 1; i <= 7; i++) {
      const nextDayIndex = (currentDayIndex + i) % 7;
      const nextDay = daysOfWeek[nextDayIndex];
      const nextSchedule = openingHours.find(
        schedule => schedule.day.toLowerCase() === nextDay
      );
      
      if (nextSchedule && nextSchedule.hours !== 'closed') {
        const dayName = nextDay.charAt(0).toUpperCase() + nextDay.slice(1);
        return {
          isOpen: false,
          nextOpenTime: `${dayName} ${nextSchedule.hours.split('-')[0]}`,
          todayHours: 'Zamknięte'
        };
      }
    }
    
    return { isOpen: false, todayHours: 'Zamknięte' };
  }

  // Parse today's hours
  const hoursMatch = todaySchedule.hours.match(/(\d{2}):(\d{2})-(\d{2}):(\d{2})/);
  if (!hoursMatch) {
    return { isOpen: false, todayHours: todaySchedule.hours };
  }

  const openTime = parseInt(hoursMatch[1]) * 60 + parseInt(hoursMatch[2]);
  const closeTime = parseInt(hoursMatch[3]) * 60 + parseInt(hoursMatch[4]);

  const isOpen = currentTime >= openTime && currentTime <= closeTime;

  return {
    isOpen,
    todayHours: todaySchedule.hours,
    nextOpenTime: isOpen ? undefined : `Dziś ${hoursMatch[1]}:${hoursMatch[2]}`
  };
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
