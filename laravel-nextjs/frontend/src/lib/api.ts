import axios from 'axios'

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

export const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// Types
export interface Company {
  id: number
  name: string
  slug: string
  description: string
  address: string
  postal_code: string
  city: string
  full_address: string
  phone?: string
  email?: string
  website?: string
  status: 'pending' | 'active' | 'inactive' | 'suspended'
  top_position?: number
  is_top: boolean
  views_count: number
  clicks_count: number
  opening_hours?: Record<string, any>
  published_at: string
  created_at: string
  updated_at: string
  category: Category
  coupons?: Coupon[]
  logo: {
    url?: string
    thumb?: string
    original?: string
  }
  gallery: Array<{
    id: number
    url: string
    thumb: string
    preview: string
    name: string
    alt: string
  }>
  meta: {
    title: string
    description: string
    keywords?: string[]
  }
  urls: {
    show: string
    click: string
  }
}

export interface Category {
  id: number
  name: string
  slug: string
  icon?: string
  color: string
  description?: string
  sort_order: number
  is_active: boolean
  companies_count: number
  active_companies_count: number
  created_at: string
  updated_at: string
  parent?: Category
  children?: Category[]
  companies?: Company[]
  urls: {
    show: string
    companies: string
  }
}

export interface Coupon {
  id: number
  title: string
  description: string
  code: string
  discount_type: 'percentage' | 'fixed' | 'free_shipping'
  discount_value: number
  minimum_amount?: number
  usage_limit?: number
  used_count: number
  company_id: number
  status: 'active' | 'inactive' | 'expired'
  valid_from?: string
  valid_until?: string
  created_at: string
  updated_at: string
  company?: Company
}

export interface PaginatedResponse<T> {
  data: T[]
  links: {
    first: string
    last: string
    prev?: string
    next?: string
  }
  meta: {
    current_page: number
    from: number
    last_page: number
    path: string
    per_page: number
    to: number
    total: number
  }
}

// API Functions
export const companiesApi = {
  getAll: (params?: {
    page?: number
    per_page?: number
    category?: string
    search?: string
    city?: string
    sort_by?: string
    sort_order?: 'asc' | 'desc'
  }) => api.get<PaginatedResponse<Company>>('/companies', { params }),

  getTop: () => api.get<{ data: Company[] }>('/companies/top'),

  getFeatured: (limit = 6) => api.get<{ data: Company[] }>('/companies/featured', { 
    params: { limit } 
  }),

  getByCategory: (categorySlug: string, params?: {
    page?: number
    per_page?: number
    search?: string
  }) => api.get<PaginatedResponse<Company>>(`/companies/category/${categorySlug}`, { params }),

  getOne: (slug: string) => api.get<{ data: Company }>(`/companies/${slug}`),

  recordClick: (companyId: number) => api.post(`/companies/${companyId}/click`),
}

export const categoriesApi = {
  getAll: () => api.get<{ data: Category[] }>('/categories'),
  getOne: (slug: string) => api.get<{ data: Category }>(`/categories/${slug}`),
}

export const couponsApi = {
  getAll: (params?: { page?: number; per_page?: number }) => 
    api.get<PaginatedResponse<Coupon>>('/coupons', { params }),
  
  getActive: (limit = 10) => 
    api.get<{ data: Coupon[] }>('/coupons/active', { params: { limit } }),
  
  getOne: (id: number) => 
    api.get<{ data: Coupon }>(`/coupons/${id}`),
  
  use: (id: number) => 
    api.post(`/coupons/${id}/use`),
}

export const statsApi = {
  get: () => api.get<{
    companies_count: number
    categories_count: number
    coupons_count: number
    top_companies: number
  }>('/stats'),
}
