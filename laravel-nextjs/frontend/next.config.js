/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost', 'zyrardow.poleca.to'],
    formats: ['image/webp', 'image/avif'],
  },
  env: {
    API_URL: process.env.API_URL || 'http://localhost:8000/api/v1',
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.API_URL || 'http://localhost:8000/api/v1'}/:path*`,
      },
    ]
  },
}

module.exports = nextConfig
