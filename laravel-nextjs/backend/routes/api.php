<?php

use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\CouponController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API Routes
Route::prefix('v1')->name('api.')->group(function () {
    
    // Categories
    Route::prefix('categories')->name('categories.')->group(function () {
        Route::get('/', [CategoryController::class, 'index'])->name('index');
        Route::get('/{category:slug}', [CategoryController::class, 'show'])->name('show');
    });
    
    // Companies
    Route::prefix('companies')->name('companies.')->group(function () {
        Route::get('/', [CompanyController::class, 'index'])->name('index');
        Route::get('/top', [CompanyController::class, 'top'])->name('top');
        Route::get('/featured', [CompanyController::class, 'featured'])->name('featured');
        Route::get('/category/{categorySlug}', [CompanyController::class, 'byCategory'])->name('by-category');
        Route::get('/{company:slug}', [CompanyController::class, 'show'])->name('show');
        Route::post('/{company}/click', [CompanyController::class, 'click'])->name('click');
    });
    
    // Coupons
    Route::prefix('coupons')->name('coupons.')->group(function () {
        Route::get('/', [CouponController::class, 'index'])->name('index');
        Route::get('/active', [CouponController::class, 'active'])->name('active');
        Route::get('/{coupon}', [CouponController::class, 'show'])->name('show');
        Route::post('/{coupon}/use', [CouponController::class, 'use'])->name('use');
    });
    
    // Search
    Route::get('/search', [CompanyController::class, 'search'])->name('search');
    
    // Stats
    Route::get('/stats', function () {
        return response()->json([
            'companies_count' => \App\Models\Company::published()->count(),
            'categories_count' => \App\Models\Category::active()->count(),
            'coupons_count' => \App\Models\Coupon::where('status', 'active')->count(),
            'top_companies' => \App\Models\Company::top()->published()->count(),
        ]);
    })->name('stats');
});

// Admin API Routes (protected by Sanctum)
Route::middleware(['auth:sanctum'])->prefix('admin')->name('admin.')->group(function () {
    
    // Categories Management
    Route::apiResource('categories', \App\Http\Controllers\Admin\CategoryController::class);
    
    // Companies Management
    Route::apiResource('companies', \App\Http\Controllers\Admin\CompanyController::class);
    Route::post('companies/{company}/set-top-position', [\App\Http\Controllers\Admin\CompanyController::class, 'setTopPosition']);
    Route::post('companies/{company}/upload-logo', [\App\Http\Controllers\Admin\CompanyController::class, 'uploadLogo']);
    
    // Coupons Management
    Route::apiResource('coupons', \App\Http\Controllers\Admin\CouponController::class);
    
    // Dashboard
    Route::get('dashboard', [\App\Http\Controllers\Admin\DashboardController::class, 'index']);
    
    // Media
    Route::post('media/upload', [\App\Http\Controllers\Admin\MediaController::class, 'upload']);
    Route::delete('media/{media}', [\App\Http\Controllers\Admin\MediaController::class, 'destroy']);
});
