<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Restauracje i Gastronomia',
                'description' => 'Restauracje, kawiarnie, bary i inne lokale gastronomiczne',
                'icon' => '🍽️',
                'color' => '#EF4444',
                'sort_order' => 1
            ],
            [
                'name' => 'Sklepy i Handel',
                'description' => 'Sklepy, centra handlowe i punkty sprzedaży',
                'icon' => '🛍️',
                'color' => '#3B82F6',
                'sort_order' => 2
            ],
            [
                'name' => 'Usługi',
                'description' => 'Różnego rodzaju usługi dla mieszkańców',
                'icon' => '🔧',
                'color' => '#10B981',
                'sort_order' => 3
            ],
            [
                'name' => 'Zdrowie i Uroda',
                'description' => 'Placówki medyczne, salony kosmetyczne, fryzjer<PERSON>',
                'icon' => '💄',
                'color' => '#F59E0B',
                'sort_order' => 4
            ],
            [
                'name' => 'Sport i Rekreacja',
                'description' => 'Siłownie, kluby sportowe, centra rekreacyjne',
                'icon' => '⚽',
                'color' => '#8B5CF6',
                'sort_order' => 5
            ],
            [
                'name' => 'Edukacja',
                'description' => 'Szkoły, kursy, korepetycje',
                'icon' => '📚',
                'color' => '#06B6D4',
                'sort_order' => 6
            ],
            [
                'name' => 'Motoryzacja',
                'description' => 'Warsztaty, stacje paliw, sklepy motoryzacyjne',
                'icon' => '🚗',
                'color' => '#84CC16',
                'sort_order' => 7
            ],
            [
                'name' => 'Dom i Ogród',
                'description' => 'Sklepy budowlane, meble, artykuły ogrodnicze',
                'icon' => '🏠',
                'color' => '#F97316',
                'sort_order' => 8
            ]
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData);
        }

        // Dodaj podkategorie dla Restauracji
        $restauracjeId = Category::where('name', 'Restauracje i Gastronomia')->first()->id;

        $subcategories = [
            [
                'name' => 'Pizzerie',
                'parent_id' => $restauracjeId,
                'icon' => '🍕',
                'color' => '#EF4444',
                'sort_order' => 1
            ],
            [
                'name' => 'Kawiarnie',
                'parent_id' => $restauracjeId,
                'icon' => '☕',
                'color' => '#EF4444',
                'sort_order' => 2
            ],
            [
                'name' => 'Fast Food',
                'parent_id' => $restauracjeId,
                'icon' => '🍔',
                'color' => '#EF4444',
                'sort_order' => 3
            ]
        ];

        foreach ($subcategories as $subcategoryData) {
            Category::create($subcategoryData);
        }
    }
}
