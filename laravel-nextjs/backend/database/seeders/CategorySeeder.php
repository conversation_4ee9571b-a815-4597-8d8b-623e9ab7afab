<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Jedzenie i Gastronomia',
                'slug' => 'jedzenie-gastronomia',
                'icon' => 'fas fa-utensils',
                'color' => '#e74c3c',
                'description' => 'Restauracje, kawiarnie, bary, catering',
                'sort_order' => 1,
            ],
            [
                'name' => 'Zdrowie i Uroda',
                'slug' => 'zdrowie-uroda',
                'icon' => 'fas fa-heart',
                'color' => '#e91e63',
                'description' => 'Salony fryzjerskie, kosmetyczne, gabinety lekarskie',
                'sort_order' => 2,
            ],
            [
                'name' => 'Zakupy i Handel',
                'slug' => 'zakupy-handel',
                'icon' => 'fas fa-shopping-bag',
                'color' => '#9c27b0',
                'description' => 'Sklepy, butiki, centra handlowe',
                'sort_order' => 3,
            ],
            [
                'name' => 'Usługi Biznesowe',
                'slug' => 'uslugi-biznesowe',
                'icon' => 'fas fa-briefcase',
                'color' => '#3f51b5',
                'description' => 'Kancelarie, biura rachunkowe, doradztwo',
                'sort_order' => 4,
            ],
            [
                'name' => 'Motoryzacja',
                'slug' => 'motoryzacja',
                'icon' => 'fas fa-car',
                'color' => '#2196f3',
                'description' => 'Warsztaty, salony samochodowe, stacje paliw',
                'sort_order' => 5,
            ],
            [
                'name' => 'Dom i Ogród',
                'slug' => 'dom-ogrod',
                'icon' => 'fas fa-home',
                'color' => '#00bcd4',
                'description' => 'Sklepy budowlane, meble, ogrodnictwo',
                'sort_order' => 6,
            ],
            [
                'name' => 'Sport i Rekreacja',
                'slug' => 'sport-rekreacja',
                'icon' => 'fas fa-dumbbell',
                'color' => '#009688',
                'description' => 'Siłownie, kluby fitness, sporty',
                'sort_order' => 7,
            ],
            [
                'name' => 'Edukacja',
                'slug' => 'edukacja',
                'icon' => 'fas fa-graduation-cap',
                'color' => '#4caf50',
                'description' => 'Szkoły, kursy, korepetycje',
                'sort_order' => 8,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData);
        }
    }
}
