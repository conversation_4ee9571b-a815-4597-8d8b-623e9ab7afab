<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Company;
use Illuminate\Database\Seeder;

class CompanySeeder extends Seeder
{
    public function run(): void
    {
        $categories = Category::all()->keyBy('slug');

        $companies = [
            [
                'name' => 'Restauracja Pod Akacjami',
                'category_slug' => 'jedzenie-gastronomia',
                'description' => 'Restauracja serwująca tradycyjną polską kuchnię w klimatycznym wnętrzu. Specjalizujemy się w daniach regionalnych przygotowywanych z najświeższych składników. Oferujemy również catering na imprezy okolicznościowe.',
                'address' => 'ul. Warszawska 15',
                'postal_code' => '96-300',
                'city' => 'Żyrardów',
                'phone' => '+48 46 855 12 34',
                'email' => '<EMAIL>',
                'website' => 'https://podakacjami.pl',
                'status' => 'active',
                'top_position' => 1,
                'opening_hours' => [
                    'monday' => ['open' => '12:00', 'close' => '22:00'],
                    'tuesday' => ['open' => '12:00', 'close' => '22:00'],
                    'wednesday' => ['open' => '12:00', 'close' => '22:00'],
                    'thursday' => ['open' => '12:00', 'close' => '22:00'],
                    'friday' => ['open' => '12:00', 'close' => '23:00'],
                    'saturday' => ['open' => '12:00', 'close' => '23:00'],
                    'sunday' => ['open' => '12:00', 'close' => '21:00'],
                ],
                'published_at' => now(),
            ],
            [
                'name' => 'Salon Fryzjerski Bella',
                'category_slug' => 'zdrowie-uroda',
                'description' => 'Nowoczesny salon fryzjerski oferujący pełen zakres usług fryzjerskich i kosmetycznych. Doświadczeni styliści, najwyższej jakości kosmetyki. Specjalizujemy się w koloryzacji, strzyżeniu i stylizacji włosów.',
                'address' => 'ul. Piłsudskiego 8',
                'postal_code' => '96-300',
                'city' => 'Żyrardów',
                'phone' => '+48 46 855 23 45',
                'email' => '<EMAIL>',
                'website' => 'https://bella-salon.pl',
                'status' => 'active',
                'top_position' => 2,
                'opening_hours' => [
                    'monday' => ['closed' => true],
                    'tuesday' => ['open' => '09:00', 'close' => '18:00'],
                    'wednesday' => ['open' => '09:00', 'close' => '18:00'],
                    'thursday' => ['open' => '09:00', 'close' => '19:00'],
                    'friday' => ['open' => '09:00', 'close' => '19:00'],
                    'saturday' => ['open' => '08:00', 'close' => '16:00'],
                    'sunday' => ['closed' => true],
                ],
                'published_at' => now(),
            ],
            [
                'name' => 'Sklep Sportowy Active',
                'category_slug' => 'zakupy-handel',
                'description' => 'Największy wybór sprzętu sportowego w Żyrardowie. Odzież, obuwie i akcesoria sportowe najlepszych marek. Profesjonalne doradztwo w wyborze sprzętu. Oferujemy również serwis sprzętu sportowego.',
                'address' => 'ul. Słowackiego 22',
                'postal_code' => '96-300',
                'city' => 'Żyrardów',
                'phone' => '+48 46 855 34 56',
                'email' => '<EMAIL>',
                'website' => 'https://active-sport.pl',
                'status' => 'active',
                'top_position' => 3,
                'opening_hours' => [
                    'monday' => ['open' => '10:00', 'close' => '19:00'],
                    'tuesday' => ['open' => '10:00', 'close' => '19:00'],
                    'wednesday' => ['open' => '10:00', 'close' => '19:00'],
                    'thursday' => ['open' => '10:00', 'close' => '19:00'],
                    'friday' => ['open' => '10:00', 'close' => '20:00'],
                    'saturday' => ['open' => '09:00', 'close' => '18:00'],
                    'sunday' => ['open' => '10:00', 'close' => '16:00'],
                ],
                'published_at' => now(),
            ],
            [
                'name' => 'Kancelaria Prawna Paragraf',
                'category_slug' => 'uslugi-biznesowe',
                'description' => 'Kompleksowe usługi prawne dla firm i osób prywatnych. Prawo cywilne, gospodarcze, rodzinne. Doświadczeni prawnicy, indywidualne podejście do każdego klienta. Oferujemy również mediacje i arbitraż.',
                'address' => 'ul. Mickiewicza 5',
                'postal_code' => '96-300',
                'city' => 'Żyrardów',
                'phone' => '+48 46 855 45 67',
                'email' => '<EMAIL>',
                'website' => 'https://paragraf-zyrardow.pl',
                'status' => 'active',
                'top_position' => null,
                'opening_hours' => [
                    'monday' => ['open' => '08:00', 'close' => '16:00'],
                    'tuesday' => ['open' => '08:00', 'close' => '16:00'],
                    'wednesday' => ['open' => '08:00', 'close' => '16:00'],
                    'thursday' => ['open' => '08:00', 'close' => '16:00'],
                    'friday' => ['open' => '08:00', 'close' => '15:00'],
                    'saturday' => ['closed' => true],
                    'sunday' => ['closed' => true],
                ],
                'published_at' => now(),
            ],
            [
                'name' => 'Warsztat Samochodowy AutoMax',
                'category_slug' => 'motoryzacja',
                'description' => 'Profesjonalny serwis samochodowy. Mechanika, elektromechanika, diagnostyka komputerowa. Serwis wszystkich marek, konkurencyjne ceny. Oferujemy również wymianę opon i przeglądy techniczne.',
                'address' => 'ul. Przemysłowa 12',
                'postal_code' => '96-300',
                'city' => 'Żyrardów',
                'phone' => '+48 46 855 56 78',
                'email' => '<EMAIL>',
                'website' => 'https://automax-zyrardow.pl',
                'status' => 'active',
                'top_position' => null,
                'opening_hours' => [
                    'monday' => ['open' => '07:00', 'close' => '17:00'],
                    'tuesday' => ['open' => '07:00', 'close' => '17:00'],
                    'wednesday' => ['open' => '07:00', 'close' => '17:00'],
                    'thursday' => ['open' => '07:00', 'close' => '17:00'],
                    'friday' => ['open' => '07:00', 'close' => '17:00'],
                    'saturday' => ['open' => '08:00', 'close' => '14:00'],
                    'sunday' => ['closed' => true],
                ],
                'published_at' => now(),
            ],
            [
                'name' => 'Pizzeria Mamma Mia',
                'category_slug' => 'jedzenie-gastronomia',
                'description' => 'Autentyczna włoska pizzeria w sercu Żyrardowa. Pizza na cienkim cieście, świeże składniki, tradycyjne przepisy. Dostawa do domu w całym Żyrardowie. Oferujemy również dania kuchni włoskiej.',
                'address' => 'ul. Kościelna 18',
                'postal_code' => '96-300',
                'city' => 'Żyrardów',
                'phone' => '+48 46 855 67 89',
                'email' => '<EMAIL>',
                'website' => 'https://mammamia-zyrardow.pl',
                'status' => 'active',
                'top_position' => null,
                'opening_hours' => [
                    'monday' => ['open' => '15:00', 'close' => '23:00'],
                    'tuesday' => ['open' => '15:00', 'close' => '23:00'],
                    'wednesday' => ['open' => '15:00', 'close' => '23:00'],
                    'thursday' => ['open' => '15:00', 'close' => '23:00'],
                    'friday' => ['open' => '15:00', 'close' => '24:00'],
                    'saturday' => ['open' => '12:00', 'close' => '24:00'],
                    'sunday' => ['open' => '12:00', 'close' => '23:00'],
                ],
                'published_at' => now(),
            ],
        ];

        foreach ($companies as $companyData) {
            $categorySlug = $companyData['category_slug'];
            unset($companyData['category_slug']);
            
            $category = $categories->get($categorySlug);
            if ($category) {
                $companyData['category_id'] = $category->id;
                Company::create($companyData);
            }
        }
    }
}
