<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\Category;

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $restauracjeId = Category::where('name', 'Restauracje i Gastronomia')->first()->id;
        $sklepyId = Category::where('name', 'Sklepy i Handel')->first()->id;
        $uslugiId = Category::where('name', 'Usługi')->first()->id;
        $zdrowieId = Category::where('name', 'Zdrowie i Uroda')->first()->id;

        $companies = [
            [
                'name' => 'Pizzeria Bella Vista',
                'short_description' => 'Najlepsza pizza w Żyrardowie z tradycyjnymi włoskimi smakami',
                'description' => 'Pizzeria Bella Vista to miejsce, gdzie tradycja spotyka się z nowoczesnością. Oferujemy autentyczne włoskie pizze przygotowywane na cienkim cieście z najwyższej jakości składników.',
                'category_id' => $restauracjeId,
                'address' => 'ul. Piękna 15, 96-300 Żyrardów',
                'phone' => '+48 46 855 12 34',
                'email' => '<EMAIL>',
                'website' => 'https://bellavista.pl',
                'opening_hours' => [
                    'monday' => '12:00-22:00',
                    'tuesday' => '12:00-22:00',
                    'wednesday' => '12:00-22:00',
                    'thursday' => '12:00-22:00',
                    'friday' => '12:00-23:00',
                    'saturday' => '12:00-23:00',
                    'sunday' => '14:00-21:00'
                ],
                'is_top' => true,
                'top_position' => 1,
                'rating' => 4.8,
                'reviews_count' => 127,
                'views_count' => 1250
            ],
            [
                'name' => 'Kawiarnia Aromat',
                'short_description' => 'Przytulna kawiarnia z najlepszą kawą w mieście',
                'description' => 'Kawiarnia Aromat to miejsce dla miłośników dobrej kawy i domowych ciast. Oferujemy szeroką gamę kaw z całego świata oraz świeże wypieki.',
                'category_id' => $restauracjeId,
                'address' => 'ul. Warszawska 8, 96-300 Żyrardów',
                'phone' => '+48 46 855 56 78',
                'email' => '<EMAIL>',
                'opening_hours' => [
                    'monday' => '07:00-19:00',
                    'tuesday' => '07:00-19:00',
                    'wednesday' => '07:00-19:00',
                    'thursday' => '07:00-19:00',
                    'friday' => '07:00-20:00',
                    'saturday' => '08:00-20:00',
                    'sunday' => '09:00-18:00'
                ],
                'is_top' => true,
                'top_position' => 2,
                'rating' => 4.6,
                'reviews_count' => 89,
                'views_count' => 890
            ],
            [
                'name' => 'Sklep Spożywczy "U Ani"',
                'short_description' => 'Lokalny sklep z świeżymi produktami',
                'description' => 'Sklep spożywczy "U Ani" to rodzinny biznes działający od ponad 20 lat. Oferujemy świeże produkty od lokalnych dostawców.',
                'category_id' => $sklepyId,
                'address' => 'ul. Kolejowa 22, 96-300 Żyrardów',
                'phone' => '+48 46 855 90 12',
                'opening_hours' => [
                    'monday' => '06:00-20:00',
                    'tuesday' => '06:00-20:00',
                    'wednesday' => '06:00-20:00',
                    'thursday' => '06:00-20:00',
                    'friday' => '06:00-20:00',
                    'saturday' => '07:00-19:00',
                    'sunday' => '08:00-16:00'
                ],
                'is_top' => false,
                'rating' => 4.3,
                'reviews_count' => 45,
                'views_count' => 567
            ],
            [
                'name' => 'Salon Fryzjerski "Metamorfoza"',
                'short_description' => 'Profesjonalne usługi fryzjerskie i kosmetyczne',
                'description' => 'Salon "Metamorfoza" oferuje kompleksowe usługi fryzjerskie, koloryzację, stylizację oraz zabiegi kosmetyczne. Nasz zespół to doświadczeni specjaliści.',
                'category_id' => $zdrowieId,
                'address' => 'ul. Słowackiego 5, 96-300 Żyrardów',
                'phone' => '+48 46 855 34 56',
                'email' => '<EMAIL>',
                'website' => 'https://metamorfoza-zyrardow.pl',
                'opening_hours' => [
                    'monday' => 'closed',
                    'tuesday' => '09:00-17:00',
                    'wednesday' => '09:00-17:00',
                    'thursday' => '09:00-19:00',
                    'friday' => '09:00-19:00',
                    'saturday' => '08:00-15:00',
                    'sunday' => 'closed'
                ],
                'is_top' => true,
                'top_position' => 3,
                'rating' => 4.9,
                'reviews_count' => 156,
                'views_count' => 1890
            ],
            [
                'name' => 'Warsztat Samochodowy "AutoSerwis"',
                'short_description' => 'Kompleksowe naprawy i serwis samochodów',
                'description' => 'AutoSerwis to nowoczesny warsztat oferujący pełen zakres usług motoryzacyjnych. Specjalizujemy się w naprawach mechanicznych i elektrycznych.',
                'category_id' => $uslugiId,
                'address' => 'ul. Przemysłowa 12, 96-300 Żyrardów',
                'phone' => '+48 46 855 78 90',
                'email' => '<EMAIL>',
                'opening_hours' => [
                    'monday' => '08:00-16:00',
                    'tuesday' => '08:00-16:00',
                    'wednesday' => '08:00-16:00',
                    'thursday' => '08:00-16:00',
                    'friday' => '08:00-16:00',
                    'saturday' => '08:00-13:00',
                    'sunday' => 'closed'
                ],
                'is_top' => false,
                'rating' => 4.4,
                'reviews_count' => 78,
                'views_count' => 654
            ]
        ];

        foreach ($companies as $companyData) {
            Company::create($companyData);
        }
    }
}
