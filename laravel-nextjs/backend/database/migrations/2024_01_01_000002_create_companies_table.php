<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            
            // Adres
            $table->string('address');
            $table->string('postal_code', 10)->nullable();
            $table->string('city')->default('Żyrardów');
            
            // Kontakt
            $table->string('phone', 20)->nullable();
            $table->string('email')->nullable();
            $table->string('website')->nullable();
            
            // Status i pozycjonowanie
            $table->enum('status', ['pending', 'active', 'inactive', 'suspended'])->default('pending');
            $table->tinyInteger('top_position')->nullable()->unique();
            
            // SEO
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->json('keywords')->nullable();
            
            // Godziny otwarcia
            $table->json('opening_hours')->nullable();
            
            // Statystyki
            $table->unsignedInteger('views_count')->default(0);
            $table->unsignedInteger('clicks_count')->default(0);
            
            // Daty
            $table->timestamp('published_at')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'top_position']);
            $table->index(['category_id', 'status']);
            $table->fullText(['name', 'description']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
