<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\CompanyResource;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CompanyController extends Controller
{
    public function index(Request $request): AnonymousResourceCollection
    {
        $query = Company::with(['category', 'media'])
            ->published();

        // Filtrowanie
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('city')) {
            $query->where('city', 'like', "%{$request->city}%");
        }

        // Sortowanie
        $sortBy = $request->get('sort_by', 'top_position');
        $sortOrder = $request->get('sort_order', 'asc');

        switch ($sortBy) {
            case 'name':
                $query->orderBy('name', $sortOrder);
                break;
            case 'created_at':
                $query->orderBy('created_at', $sortOrder);
                break;
            case 'views':
                $query->orderBy('views_count', $sortOrder);
                break;
            default:
                $query->orderByRaw('CASE WHEN top_position IS NOT NULL THEN top_position ELSE 999 END ASC')
                    ->orderBy('name');
        }

        $companies = $query->paginate($request->get('per_page', 12));

        return CompanyResource::collection($companies);
    }

    public function show(Company $company): CompanyResource
    {
        $company->load(['category', 'media', 'activeCoupons']);
        $company->incrementViews();

        return new CompanyResource($company);
    }

    public function top(): AnonymousResourceCollection
    {
        $companies = Company::with(['category', 'media'])
            ->top()
            ->published()
            ->limit(3)
            ->get();

        return CompanyResource::collection($companies);
    }

    public function featured(Request $request): AnonymousResourceCollection
    {
        $limit = $request->get('limit', 6);
        
        $companies = Company::with(['category', 'media'])
            ->published()
            ->where('views_count', '>', 0)
            ->orderBy('views_count', 'desc')
            ->limit($limit)
            ->get();

        return CompanyResource::collection($companies);
    }

    public function byCategory(Request $request, string $categorySlug): AnonymousResourceCollection
    {
        $query = Company::with(['category', 'media'])
            ->published()
            ->whereHas('category', function ($q) use ($categorySlug) {
                $q->where('slug', $categorySlug);
            });

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        $companies = $query->orderByRaw('CASE WHEN top_position IS NOT NULL THEN top_position ELSE 999 END ASC')
            ->orderBy('name')
            ->paginate($request->get('per_page', 12));

        return CompanyResource::collection($companies);
    }

    public function click(Company $company)
    {
        $company->incrementClicks();

        return response()->json([
            'success' => true,
            'message' => 'Click recorded'
        ]);
    }
}
