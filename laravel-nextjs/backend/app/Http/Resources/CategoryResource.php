<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'icon' => $this->icon,
            'color' => $this->color,
            'description' => $this->description,
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
            'companies_count' => $this->companies_count,
            'active_companies_count' => $this->active_companies_count,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relacje
            'parent' => new CategoryResource($this->whenLoaded('parent')),
            'children' => CategoryResource::collection($this->whenLoaded('children')),
            'companies' => CompanyResource::collection($this->whenLoaded('companies')),
            
            // URLs
            'urls' => [
                'show' => route('api.categories.show', $this->slug),
                'companies' => route('api.companies.by-category', $this->slug),
            ],
        ];
    }
}
