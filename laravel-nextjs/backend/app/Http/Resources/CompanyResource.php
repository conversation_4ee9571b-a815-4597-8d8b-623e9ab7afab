<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'address' => $this->address,
            'postal_code' => $this->postal_code,
            'city' => $this->city,
            'full_address' => $this->full_address,
            'phone' => $this->phone,
            'email' => $this->email,
            'website' => $this->website,
            'status' => $this->status,
            'top_position' => $this->top_position,
            'is_top' => $this->is_top,
            'views_count' => $this->views_count,
            'clicks_count' => $this->clicks_count,
            'opening_hours' => $this->opening_hours,
            'published_at' => $this->published_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relacje
            'category' => new CategoryResource($this->whenLoaded('category')),
            'coupons' => CouponResource::collection($this->whenLoaded('activeCoupons')),
            
            // Media
            'logo' => [
                'url' => $this->logo_url,
                'thumb' => $this->getFirstMediaUrl('logo', 'thumb'),
                'original' => $this->getFirstMediaUrl('logo'),
            ],
            'gallery' => $this->getMedia('gallery')->map(function ($media) {
                return [
                    'id' => $media->id,
                    'url' => $media->getUrl(),
                    'thumb' => $media->getUrl('thumb'),
                    'preview' => $media->getUrl('preview'),
                    'name' => $media->name,
                    'alt' => $media->getCustomProperty('alt', ''),
                ];
            }),
            
            // SEO
            'meta' => [
                'title' => $this->meta_title ?: $this->name,
                'description' => $this->meta_description ?: \Str::limit($this->description, 160),
                'keywords' => $this->keywords,
            ],
            
            // URLs
            'urls' => [
                'show' => route('api.companies.show', $this->slug),
                'click' => route('api.companies.click', $this->id),
            ],
        ];
    }
}
