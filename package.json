{"name": "zyrardow-poleca-portal", "version": "1.0.0", "description": "Portal lokalny Żyrardów.poleca.to z panelem administracyjnym", "main": "server/app.js", "scripts": {"start": "node server/app.js", "dev": "nodemon server/app.js", "setup-db": "node server/scripts/setup-database.js", "seed": "node server/scripts/seed-database.js"}, "keywords": ["portal", "lokalny", "zyrardow", "cms"], "author": "Żyrardów.poleca.to", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "sharp": "^0.33.5", "slugify": "^1.6.6"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}