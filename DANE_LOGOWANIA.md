# Dane logowania do panelu administracyjnego

## 🔐 Dostęp do panelu admin

**URL panelu:** `/admin/index.html`

### Dane logowania:

1. **Email:** `<EMAIL>`  
   **Hasło:** `admin123`

2. **Email:** `<EMAIL>`  
   **Hasło:** `admin123`

3. **Email:** `admin`  
   **Hasło:** `admin123`

## 📋 Funkcjonalności panelu

### Zarządzanie firmami TOP
- Przejdź do sekcji "Firmy" w menu
- Kliknij ikonę gwiazdki (⭐) przy wybranej firmie
- Wybierz pozycję TOP 1, 2, 3 lub usuń z rankingu
- Zapisz zmiany

### System plików JSON
Panel używa systemu plików JSON zamiast bazy danych MySQL:
- **Firmy:** `admin/data/companies.json`
- **Kate<PERSON>ie:** `admin/data/categories.json`
- **Kupony:** dane przykładowe w API
- **Oferty:** dane przykładowe w API

## 🔧 Naprawione problemy

### 1. ✅ Dane logowania
- Ustawiono stałe dane logowania <NAME_EMAIL>
- System sesji działa przez 24 godziny

### 2. ✅ Page-hero fade-in
- Dodano brakujące sekcje page-hero na stronach:
  - `atrakcje.html`
  - `o-miescie.html`
- Przywrócono animacje fade-in

### 3. ✅ Strzałki w footer
- Naprawiono problem z wyświetlaniem strzałek w menu footer
- Zmieniono z znaku `›` na ikonę Font Awesome `\f105`

### 4. ✅ System bez MySQL
- Zastąpiono MySQL systemem plików JSON
- API działa z plikami lokalnymi
- Automatyczne tworzenie katalogów danych

## 🚀 Testowanie systemu

1. **Zaloguj się do panelu admin**
2. **Przejdź do sekcji Firmy**
3. **Przetestuj ustawianie pozycji TOP**
4. **Sprawdź wyświetlanie na stronie głównej**

## 📁 Struktura plików

```
admin/
├── data/
│   ├── companies.json     # Dane firm
│   └── categories.json    # Kategorie
├── api/
│   ├── companies.php      # API firm (JSON)
│   ├── coupons.php        # API kuponów
│   └── offers.php         # API ofert
├── css/
│   └── top-positions.css  # Style dla TOP
└── js/
    ├── admin.js           # Logowanie
    └── companies.js       # Zarządzanie firmami
```

## 🔄 Backup danych

Regularnie rób kopie zapasowe plików:
- `admin/data/companies.json`
- `admin/data/categories.json`

## 📞 Wsparcie

W przypadku problemów:
- Email: <EMAIL>
- Sprawdź logi błędów w konsoli przeglądarki
- Upewnij się, że katalog `admin/data/` ma uprawnienia do zapisu
