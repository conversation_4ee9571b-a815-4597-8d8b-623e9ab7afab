<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wiadomości lokalne - Żyrardów Poleca</title>
    <meta name="description" content="Najnowsze wiadomości z Żyrardowa - wydarzenia lokalne, inwestycje, kultura, sport i biznes w naszym mieście">
    <meta name="keywords" content="wiadomości Żyrardów, wydarzenia lokalne, aktualności Żyrardów, news">

    <!-- Open Graph -->
    <meta property="og:title" content="Wiadomości lokalne - Żyrardów Poleca">
    <meta property="og:description" content="Najnowsze wiadomości z Żyrardowa - wydarzenia lokalne, inwestycje, kultura, sport i biznes">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://zyrardow.poleca.to/wiadomosci.html">
    <meta property="og:image" content="https://zyrardow.poleca.to/images/og-news.jpg">

    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/menu.css">
    <link rel="stylesheet" href="css/wiadomosci.css">

    <!-- PWA -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#ff6600">
</head>
<body>
    <header class="site-header">
        <div class="container">
            <nav class="main-nav">
                <div class="logo">
                    <a href="index.html">
                        <img src="images/logo-zyrardow-poleca.png" alt="Żyrardów.poleca.to" width="200" height="60">
                    </a>
                </div>
                <ul class="nav-links">
                    <li class="has-submenu">
                        <a href="o-miescie.html" id="nav-city">O mieście</a>
                        <ul class="submenu">
                            <li><a href="o-miescie.html" id="nav-about">O Żyrardowie</a></li>
                            <li><a href="historia.html" id="nav-history">Historia</a></li>
                            <li><a href="zabytki.html" id="nav-monuments">Zabytki</a></li>
                            <li><a href="powiat.html" id="nav-county">Powiat żyrardowski</a></li>
                        </ul>
                    </li>
                    <li class="has-submenu">
                        <a href="atrakcje.html" id="nav-attractions">Atrakcje</a>
                        <ul class="submenu">
                            <li><a href="atrakcje.html" id="nav-places">Miejsca warte odwiedzenia</a></li>
                            <li><a href="osada-fabryczna.html" id="nav-factory">Osada fabryczna</a></li>
                            <li><a href="park-dittricha.html" id="nav-park">Park Dittricha</a></li>
                            <li><a href="muzeum-lniarstwa.html" id="nav-museum">Muzeum Lniarstwa</a></li>
                            <li><a href="kosciol-mb-pocieszenia.html" id="nav-church">Kościół MB Pocieszenia</a></li>
                            <li><a href="centrum-kultury.html" id="nav-culture-center">Centrum Kultury</a></li>
                            <li><a href="aquapark.html" id="nav-aquapark">Aquapark</a></li>
                            <li><a href="kultura-sport.html" id="nav-culture-sport">Kultura i sport</a></li>
                        </ul>
                    </li>
                    <li><a href="turystyka.html" id="nav-tourism">Turystyka</a></li>
                    <li><a href="kontakt.html" id="nav-contact">Kontakt</a></li>
                </ul>
                <div class="nav-actions">
                    <a href="https://facebook.com/zyrardow.poleca.to" class="social-icon" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <button class="mobile-menu-btn" aria-label="Menu mobilne">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </nav>
            <div class="search-container">
                <form class="search-form">
                    <input type="text" placeholder="Szukaj w serwisie..." aria-label="Szukaj w serwisie">
                    <button type="submit" aria-label="Szukaj"><i class="fas fa-search"></i></button>
                </form>
            </div>
        </div>
    </header>
        </div>
    </section>

    <!-- Main Content -->
    <main class="news-main">
        <div class="container">
            <!-- Filtry kategorii -->
            <section class="news-filters">
                <div class="filters-header">
                    <h2>Kategorie wiadomości</h2>
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="newsSearch" placeholder="Szukaj wiadomości...">
                    </div>
                </div>
                <div class="category-filters">
                    <button class="category-btn active" data-category="all">
                        <i class="fas fa-th-large"></i>
                        Wszystkie
                        <span class="count" id="countAll">0</span>
                    </button>
                    <button class="category-btn" data-category="local">
                        <i class="fas fa-map-marker-alt"></i>
                        Lokalne
                        <span class="count" id="countLocal">0</span>
                    </button>
                    <button class="category-btn" data-category="events">
                        <i class="fas fa-calendar-alt"></i>
                        Wydarzenia
                        <span class="count" id="countEvents">0</span>
                    </button>
                    <button class="category-btn" data-category="business">
                        <i class="fas fa-briefcase"></i>
                        Biznes
                        <span class="count" id="countBusiness">0</span>
                    </button>
                    <button class="category-btn" data-category="culture">
                        <i class="fas fa-theater-masks"></i>
                        Kultura
                        <span class="count" id="countCulture">0</span>
                    </button>
                    <button class="category-btn" data-category="sport">
                        <i class="fas fa-futbol"></i>
                        Sport
                        <span class="count" id="countSport">0</span>
                    </button>
                </div>
            </section>

            <!-- Lista wiadomości -->
            <section class="news-content">
                <div class="content-header">
                    <div class="results-info">
                        <span id="resultsCount">Ładowanie wiadomości...</span>
                    </div>
                    <div class="sort-options">
                        <label for="sortSelect">Sortuj:</label>
                        <select id="sortSelect">
                            <option value="newest">Najnowsze</option>
                            <option value="oldest">Najstarsze</option>
                            <option value="title">Alfabetycznie</option>
                        </select>
                    </div>
                </div>

                <!-- Grid wiadomości -->
                <div class="news-grid" id="newsGrid">
                    <!-- Wiadomości będą ładowane dynamicznie -->
                </div>

                <!-- Paginacja -->
                <div class="pagination" id="pagination">
                    <!-- Paginacja będzie generowana dynamicznie -->
                </div>

                <!-- Stan pustej listy -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h3>Brak wiadomości</h3>
                    <p>Nie znaleziono wiadomości spełniających kryteria wyszukiwania.</p>
                    <button class="btn btn-primary" onclick="resetFilters()">
                        <i class="fas fa-redo"></i>
                        Resetuj filtry
                    </button>
                </div>

                <!-- Loading state -->
                <div class="loading-state" id="loadingState">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <p>Ładowanie wiadomości...</p>
                </div>
            </section>
        </div>
    </main>



    <footer class="site-footer">
        <div class="container">
            <div class="footer-top">
                <div class="footer-logo">
                    <img src="images/logo-zyrardow-poleca-white.jpg" alt="Żyrardów.poleca.to" width="180" height="54">
                    <p>Kompleksowy portal miejski o Żyrardowie</p>
                    <div class="footer-social">
                        <h5>Śledź nas</h5>
                        <div class="social-icons">
                            <a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://instagram.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer" aria-label="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="footer-nav">
                    <div class="footer-nav-column">
                        <h3>O mieście</h3>
                        <ul>
                            <li><a href="o-miescie.html">O Żyrardowie</a></li>
                            <li><a href="historia.html">Historia</a></li>
                            <li><a href="zabytki.html">Zabytki</a></li>
                            <li><a href="atrakcje.html">Atrakcje</a></li>
                        </ul>
                    </div>
                    <div class="footer-nav-column">
                        <h3>Turystyka</h3>
                        <ul>
                            <li><a href="turystyka.html">Informacje turystyczne</a></li>
                            <li><a href="atrakcje.html">Miejsca warte odwiedzenia</a></li>
                            <li><a href="zabytki.html">Zabytki przemysłowe</a></li>
                        </ul>
                    </div>
                    <div class="footer-nav-column">
                        <h3>Kontakt</h3>
                        <ul>
                            <li><a href="kontakt.html">Skontaktuj się z nami</a></li>
                            <li><a href="mailto:<EMAIL>"><EMAIL></a></li>
                            <li><a href="https://facebook.com/zyrardow.poleca.to" target="_blank" rel="noopener noreferrer">Facebook</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Żyrardów.poleca.to - Wszelkie prawa zastrzeżone</p>
                <ul class="footer-links">
                    <li><a href="polityka-prywatnosci.html">Polityka prywatności</a></li>
                    <li><a href="regulamin.html">Regulamin</a></li>
                    <li><a href="mapa-strony.html">Mapa strony</a></li>
                    <li><a href="kontakt.html">Kontakt</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <div class="cookie-notice" id="cookieNotice">
        <div class="container">
            <p>Strona wykorzystuje pliki cookies, aby zapewnić najlepsze doświadczenia. Kontynuując przeglądanie strony, wyrażasz zgodę na używanie plików cookies.</p>
            <div class="cookie-buttons">
                <button class="btn btn-primary btn-sm" id="acceptCookies">Akceptuję</button>
                <a href="polityka-prywatnosci.html" class="btn btn-outline btn-sm">Więcej informacji</a>
            </div>
        </div>
    </div>

    <a href="#" class="back-to-top" aria-label="Przewiń do góry">
        <i class="fas fa-chevron-up"></i>
    </a>
            <div class="modal-body" id="newsModalBody">
                <!-- Treść wiadomości będzie ładowana dynamicznie -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
