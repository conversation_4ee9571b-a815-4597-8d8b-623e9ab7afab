/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla zarządzania kategoriami
 */

// Globalne dane kategorii
let categoriesData = [];

document.addEventListener('DOMContentLoaded', function() {
    // Załaduj dane kategorii z localStorage lub inicjalizuj domyślne
    loadCategoriesData();

    // Inicjalizacja funkcji zarządzania kategoriami
    initCategoriesManagement();
});

/**
 * Załaduj dane kategorii z localStorage lub inicjalizuj domyślne
 */
function loadCategoriesData() {
    const stored = localStorage.getItem('zyrardow_categories_data');
    if (stored) {
        try {
            categoriesData = JSON.parse(stored);
            console.log('Załadowano kategorie z localStorage:', categoriesData);
        } catch (error) {
            console.error('Błąd podczas ładowania kategorii z localStorage:', error);
            initDefaultCategories();
        }
    } else {
        initDefaultCategories();
    }

    // Renderuj kategorie na podstawie załadowanych danych
    renderCategoriesFromData();
}

/**
 * Inicjalizuj domyślne kategorie
 */
function initDefaultCategories() {
    categoriesData = [
        {
            id: 1,
            name: 'Jedzenie i Gastronomia',
            icon: 'fa-utensils',
            slug: 'jedzenie-i-gastronomia',
            description: 'Restauracje, kawiarnie, bary i inne lokale gastronomiczne w Żyrardowie.',
            status: true,
            subcategories: [
                { id: 101, name: 'Restauracje', slug: 'restauracje' },
                { id: 102, name: 'Pizzerie', slug: 'pizzerie' },
                { id: 103, name: 'Fast Food', slug: 'fast-food' },
                { id: 104, name: 'Kawiarnie i herbaciarnie', slug: 'kawiarnie-i-herbaciarnie' },
                { id: 105, name: 'Cukiernie i piekarnie', slug: 'cukiernie-i-piekarnie' }
            ]
        },
        {
            id: 2,
            name: 'Zdrowie i Uroda',
            icon: 'fa-heartbeat',
            slug: 'zdrowie-i-uroda',
            description: 'Przychodnie, apteki, salony kosmetyczne i fryzjerskie w Żyrardowie.',
            status: true,
            subcategories: [
                { id: 201, name: 'Przychodnie i gabinety', slug: 'przychodnie-i-gabinety' },
                { id: 202, name: 'Apteki', slug: 'apteki' },
                { id: 203, name: 'Salony kosmetyczne', slug: 'salony-kosmetyczne' }
            ]
        },
        {
            id: 3,
            name: 'Dom i Ogród',
            icon: 'fa-home',
            slug: 'dom-i-ogrod',
            description: 'Sklepy meblowe, budowlane i usługi dla domu w Żyrardowie.',
            status: true,
            subcategories: [
                { id: 301, name: 'Sklepy meblowe', slug: 'sklepy-meblowe' },
                { id: 302, name: 'Sklepy budowlane', slug: 'sklepy-budowlane' }
            ]
        }
    ];

    saveCategoriesData();
}

/**
 * Zapisz dane kategorii do localStorage
 */
function saveCategoriesData() {
    try {
        localStorage.setItem('zyrardow_categories_data', JSON.stringify(categoriesData));

        // Zapisz również w formacie dla frontend
        const frontendCategories = convertToFrontendFormat(categoriesData);
        localStorage.setItem('zyrardow_frontend_categories', JSON.stringify(frontendCategories));

        console.log('Zapisano kategorie do localStorage');

        // Wyślij event o zmianie kategorii
        window.dispatchEvent(new CustomEvent('categoriesUpdated', {
            detail: { categories: categoriesData, frontendCategories: frontendCategories }
        }));

    } catch (error) {
        console.error('Błąd podczas zapisywania kategorii:', error);
    }
}

/**
 * Konwertuj dane kategorii do formatu frontend
 */
function convertToFrontendFormat(categories) {
    return categories.filter(cat => cat.status).map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        slug: category.slug,
        dataCategory: category.slug.replace(/-/g, ''),
        subcategories: category.subcategories.map(sub => ({
            id: sub.id,
            name: sub.name,
            slug: sub.slug,
            dataSubcategory: sub.slug.replace(/-/g, '')
        }))
    }));
}

/**
 * Inicjalizacja funkcji zarządzania kategoriami
 */
function initCategoriesManagement() {
    // Obsługa wyszukiwania kategorii
    initCategorySearch();

    // Obsługa drzewa kategorii
    initCategoryTree();

    // Obsługa formularza edycji kategorii
    initCategoryForm();

    // Obsługa modalu dodawania kategorii
    initAddCategoryModal();

    // Obsługa modalu dodawania podkategorii
    initAddSubcategoryModal();

    // Obsługa selektora ikon
    initIconSelector();
}

/**
 * Renderuj kategorie na podstawie danych z localStorage
 */
function renderCategoriesFromData() {
    const categoriesTree = document.getElementById('categories-tree');
    if (!categoriesTree) return;

    // Wyczyść istniejące kategorie
    categoriesTree.innerHTML = '';

    // Renderuj każdą kategorię
    categoriesData.forEach((category, index) => {
        const categoryHTML = `
            <li class="category-item ${index === 0 ? 'active' : ''}" data-id="${category.id}">
                <div class="category-item-header">
                    <i class="fas ${category.icon} category-icon"></i>
                    <span class="category-name">${category.name}</span>
                    <div class="category-actions">
                        <button class="btn-icon add-subcategory" title="Dodaj podkategorię"><i class="fas fa-plus"></i></button>
                        <button class="btn-icon edit-category" title="Edytuj kategorię"><i class="fas fa-edit"></i></button>
                        <button class="btn-icon delete-category" title="Usuń kategorię"><i class="fas fa-trash-alt"></i></button>
                    </div>
                </div>
                <ul class="subcategories">
                    ${category.subcategories.map(sub => `
                        <li class="subcategory-item" data-id="${sub.id}">
                            <div class="subcategory-item-header">
                                <span class="subcategory-name">${sub.name}</span>
                                <div class="subcategory-actions">
                                    <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                    <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                                </div>
                            </div>
                        </li>
                    `).join('')}
                </ul>
            </li>
        `;

        categoriesTree.insertAdjacentHTML('beforeend', categoryHTML);
    });

    // Ponownie zainicjalizuj obsługę zdarzeń dla nowo utworzonych elementów
    reinitializeEventHandlers();
}

/**
 * Ponownie zainicjalizuj obsługę zdarzeń
 */
function reinitializeEventHandlers() {
    // Usuń stare event listenery i dodaj nowe
    initCategoryTree();
}

/**
 * Inicjalizacja wyszukiwania kategorii
 */
function initCategorySearch() {
    const searchInput = document.getElementById('category-search');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const categoryItems = document.querySelectorAll('.category-item');

            categoryItems.forEach(item => {
                const categoryName = item.querySelector('.category-name').textContent.toLowerCase();
                const subcategoryItems = item.querySelectorAll('.subcategory-item');
                let hasMatchingSubcategory = false;

                // Sprawdzenie, czy któraś z podkategorii pasuje do wyszukiwania
                subcategoryItems.forEach(subitem => {
                    const subcategoryName = subitem.querySelector('.subcategory-name').textContent.toLowerCase();

                    if (subcategoryName.includes(searchTerm)) {
                        subitem.style.display = '';
                        hasMatchingSubcategory = true;
                    } else {
                        subitem.style.display = 'none';
                    }
                });

                // Wyświetlenie kategorii, jeśli nazwa kategorii pasuje lub ma pasującą podkategorię
                if (categoryName.includes(searchTerm) || hasMatchingSubcategory) {
                    item.style.display = '';
                    if (hasMatchingSubcategory) {
                        item.classList.add('active'); // Rozwinięcie kategorii, jeśli ma pasującą podkategorię
                    }
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
}

/**
 * Inicjalizacja drzewa kategorii
 */
function initCategoryTree() {
    const categoryItems = document.querySelectorAll('.category-item-header');

    // Obsługa kliknięcia na kategorię
    categoryItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // Ignorowanie kliknięć na przyciski akcji
            if (e.target.closest('.category-actions')) {
                return;
            }

            const categoryItem = this.closest('.category-item');

            // Usunięcie aktywnej klasy z wszystkich kategorii
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });

            // Dodanie aktywnej klasy do klikniętej kategorii
            categoryItem.classList.add('active');

            // Załadowanie danych kategorii do formularza
            loadCategoryDetails(categoryItem);
        });
    });

    // Obsługa przycisków akcji dla kategorii
    const addSubcategoryButtons = document.querySelectorAll('.add-subcategory');
    const editCategoryButtons = document.querySelectorAll('.edit-category');
    const deleteCategoryButtons = document.querySelectorAll('.delete-category');

    // Dodawanie podkategorii
    addSubcategoryButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const categoryItem = this.closest('.category-item');
            const categoryId = categoryItem.getAttribute('data-id');
            const categoryName = categoryItem.querySelector('.category-name').textContent;

            // Otwarcie modalu dodawania podkategorii
            openAddSubcategoryModal(categoryId, categoryName);
        });
    });

    // Edycja kategorii
    editCategoryButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const categoryItem = this.closest('.category-item');

            // Usunięcie aktywnej klasy z wszystkich kategorii
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });

            // Dodanie aktywnej klasy do klikniętej kategorii
            categoryItem.classList.add('active');

            // Załadowanie danych kategorii do formularza
            loadCategoryDetails(categoryItem);
        });
    });

    // Usuwanie kategorii
    deleteCategoryButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const categoryItem = this.closest('.category-item');
            const categoryName = categoryItem.querySelector('.category-name').textContent;

            if (confirm(`Czy na pewno chcesz usunąć kategorię "${categoryName}" wraz z wszystkimi podkategoriami?`)) {
                const categoryId = parseInt(categoryItem.getAttribute('data-id'));

                // Usuń kategorię z danych
                const categoryIndex = categoriesData.findIndex(cat => cat.id === categoryId);
                if (categoryIndex !== -1) {
                    categoriesData.splice(categoryIndex, 1);

                    // Zapisz do localStorage
                    saveCategoriesData();

                    // Usuń z DOM
                    categoryItem.remove();
                    showNotification(`Kategoria "${categoryName}" została usunięta i zsynchronizowana z frontend.`, 'success');

                    // Wyczyszczenie formularza, jeśli usunięta kategoria była aktywna
                    if (categoryItem.classList.contains('active')) {
                        clearCategoryForm();
                    }
                }
            }
        });
    });

    // Obsługa przycisków akcji dla podkategorii
    const editSubcategoryButtons = document.querySelectorAll('.edit-subcategory');
    const deleteSubcategoryButtons = document.querySelectorAll('.delete-subcategory');

    // Edycja podkategorii
    editSubcategoryButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const subcategoryItem = this.closest('.subcategory-item');
            const subcategoryName = subcategoryItem.querySelector('.subcategory-name').textContent;

            // W rzeczywistej implementacji tutaj byłoby ładowanie danych podkategorii do formularza
            alert(`Edycja podkategorii "${subcategoryName}"`);
        });
    });

    // Usuwanie podkategorii
    deleteSubcategoryButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const subcategoryItem = this.closest('.subcategory-item');
            const subcategoryName = subcategoryItem.querySelector('.subcategory-name').textContent;

            if (confirm(`Czy na pewno chcesz usunąć podkategorię "${subcategoryName}"?`)) {
                const subcategoryId = parseInt(subcategoryItem.getAttribute('data-id'));
                const categoryItem = subcategoryItem.closest('.category-item');
                const categoryId = parseInt(categoryItem.getAttribute('data-id'));

                // Usuń podkategorię z danych
                const categoryIndex = categoriesData.findIndex(cat => cat.id === categoryId);
                if (categoryIndex !== -1) {
                    const subcategoryIndex = categoriesData[categoryIndex].subcategories.findIndex(sub => sub.id === subcategoryId);
                    if (subcategoryIndex !== -1) {
                        categoriesData[categoryIndex].subcategories.splice(subcategoryIndex, 1);

                        // Zapisz do localStorage
                        saveCategoriesData();
                    }
                }

                subcategoryItem.remove();
                showNotification(`Podkategoria "${subcategoryName}" została usunięta i zsynchronizowana z frontend.`, 'success');
            }
        });
    });
}

/**
 * Inicjalizacja formularza edycji kategorii
 */
function initCategoryForm() {
    const categoryForm = document.getElementById('category-form');
    const cancelButton = document.getElementById('cancel-btn');

    if (categoryForm) {
        categoryForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Pobranie danych z formularza
            const categoryName = document.getElementById('category-name-input').value;
            const categoryIcon = document.querySelector('.selected-icon i').className.split(' ')[1];
            const categoryDescription = document.getElementById('category-description').value;
            const categorySlug = document.getElementById('category-slug').value;
            const categoryStatus = document.getElementById('category-status').checked;

            // Walidacja formularza
            if (!categoryName) {
                alert('Nazwa kategorii jest wymagana.');
                return;
            }

            // Aktualizacja danych kategorii w drzewie i localStorage
            const activeCategory = document.querySelector('.category-item.active');
            if (activeCategory) {
                const categoryId = parseInt(activeCategory.getAttribute('data-id'));

                // Znajdź kategorię w danych i zaktualizuj
                const categoryIndex = categoriesData.findIndex(cat => cat.id === categoryId);
                if (categoryIndex !== -1) {
                    categoriesData[categoryIndex].name = categoryName;
                    categoriesData[categoryIndex].icon = categoryIcon;
                    categoriesData[categoryIndex].description = categoryDescription;
                    categoriesData[categoryIndex].slug = categorySlug;
                    categoriesData[categoryIndex].status = categoryStatus;

                    // Zapisz do localStorage
                    saveCategoriesData();

                    // Aktualizuj widok
                    activeCategory.querySelector('.category-name').textContent = categoryName;
                    activeCategory.querySelector('.category-icon').className = `fas ${categoryIcon} category-icon`;

                    showNotification('Kategoria została zaktualizowana i zsynchronizowana z frontend.', 'success');
                }
            }
        });
    }

    if (cancelButton) {
        cancelButton.addEventListener('click', function() {
            // Przywrócenie oryginalnych danych kategorii
            const activeCategory = document.querySelector('.category-item.active');
            if (activeCategory) {
                loadCategoryDetails(activeCategory);
            }
        });
    }
}

/**
 * Inicjalizacja modalu dodawania kategorii
 */
function initAddCategoryModal() {
    const addCategoryBtn = document.getElementById('add-category-btn');
    const addCategoryModal = document.getElementById('addCategoryModal');
    const addCategoryForm = document.getElementById('add-category-form');
    const closeButtons = addCategoryModal.querySelectorAll('.modal-close, .modal-cancel');

    // Otwieranie modalu
    if (addCategoryBtn) {
        addCategoryBtn.addEventListener('click', function() {
            addCategoryModal.classList.add('show');
        });
    }

    // Zamykanie modalu
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            addCategoryModal.classList.remove('show');
        });
    });

    // Obsługa kliknięcia poza modalem
    window.addEventListener('click', function(e) {
        if (e.target === addCategoryModal) {
            addCategoryModal.classList.remove('show');
        }
    });

    // Obsługa formularza dodawania kategorii
    if (addCategoryForm) {
        addCategoryForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Pobranie danych z formularza
            const categoryName = document.getElementById('new-category-name').value;
            const categoryIcon = document.querySelector('#addCategoryModal .selected-icon i').className.split(' ')[1];
            const categoryDescription = document.getElementById('new-category-description').value;

            // Walidacja formularza
            if (!categoryName) {
                alert('Nazwa kategorii jest wymagana.');
                return;
            }

            // Generowanie ID dla nowej kategorii
            const newCategoryId = Math.max(...categoriesData.map(cat => cat.id), 0) + 1;
            const categorySlug = categoryName.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '');

            // Dodanie nowej kategorii do danych
            const newCategory = {
                id: newCategoryId,
                name: categoryName,
                icon: categoryIcon,
                slug: categorySlug,
                description: categoryDescription || `Kategoria ${categoryName} w Żyrardowie.`,
                status: true,
                subcategories: []
            };

            categoriesData.push(newCategory);

            // Zapisz do localStorage
            saveCategoriesData();

            // Tworzenie nowej kategorii w DOM
            const newCategoryHTML = `
                <li class="category-item" data-id="${newCategoryId}">
                    <div class="category-item-header">
                        <i class="fas ${categoryIcon} category-icon"></i>
                        <span class="category-name">${categoryName}</span>
                        <div class="category-actions">
                            <button class="btn-icon add-subcategory" title="Dodaj podkategorię"><i class="fas fa-plus"></i></button>
                            <button class="btn-icon edit-category" title="Edytuj kategorię"><i class="fas fa-edit"></i></button>
                            <button class="btn-icon delete-category" title="Usuń kategorię"><i class="fas fa-trash-alt"></i></button>
                        </div>
                    </div>
                    <ul class="subcategories"></ul>
                </li>
            `;

            // Dodanie nowej kategorii do drzewa
            const categoriesTree = document.getElementById('categories-tree');
            categoriesTree.insertAdjacentHTML('beforeend', newCategoryHTML);

            // Dodanie obsługi zdarzeń dla nowej kategorii
            const newCategory = categoriesTree.lastElementChild;

            // Obsługa kliknięcia na kategorię
            newCategory.querySelector('.category-item-header').addEventListener('click', function(e) {
                // Ignorowanie kliknięć na przyciski akcji
                if (e.target.closest('.category-actions')) {
                    return;
                }

                // Usunięcie aktywnej klasy z wszystkich kategorii
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Dodanie aktywnej klasy do klikniętej kategorii
                newCategory.classList.add('active');

                // Załadowanie danych kategorii do formularza
                loadCategoryDetails(newCategory);
            });

            // Obsługa przycisków akcji
            newCategory.querySelector('.add-subcategory').addEventListener('click', function(e) {
                e.stopPropagation();
                openAddSubcategoryModal(newCategoryId, categoryName);
            });

            newCategory.querySelector('.edit-category').addEventListener('click', function(e) {
                e.stopPropagation();
                document.querySelectorAll('.category-item').forEach(item => {
                    item.classList.remove('active');
                });
                newCategory.classList.add('active');
                loadCategoryDetails(newCategory);
            });

            newCategory.querySelector('.delete-category').addEventListener('click', function(e) {
                e.stopPropagation();
                if (confirm(`Czy na pewno chcesz usunąć kategorię "${categoryName}" wraz z wszystkimi podkategoriami?`)) {
                    newCategory.remove();
                    showNotification(`Kategoria "${categoryName}" została usunięta.`, 'success');
                    if (newCategory.classList.contains('active')) {
                        clearCategoryForm();
                    }
                }
            });

            // Zamknięcie modalu i wyczyszczenie formularza
            addCategoryModal.classList.remove('show');
            addCategoryForm.reset();

            // Wyświetlenie powiadomienia
            showNotification(`Kategoria "${categoryName}" została dodana.`, 'success');
        });
    }
}

/**
 * Inicjalizacja modalu dodawania podkategorii
 */
function initAddSubcategoryModal() {
    const addSubcategoryModal = document.getElementById('addSubcategoryModal');
    const addSubcategoryForm = document.getElementById('add-subcategory-form');
    const closeButtons = addSubcategoryModal.querySelectorAll('.modal-close, .modal-cancel');

    // Zamykanie modalu
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            addSubcategoryModal.classList.remove('show');
        });
    });

    // Obsługa kliknięcia poza modalem
    window.addEventListener('click', function(e) {
        if (e.target === addSubcategoryModal) {
            addSubcategoryModal.classList.remove('show');
        }
    });

    // Obsługa formularza dodawania podkategorii
    if (addSubcategoryForm) {
        addSubcategoryForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Pobranie danych z formularza
            const parentCategoryId = document.getElementById('parent-category-id').value;
            const subcategoryName = document.getElementById('new-subcategory-name').value;

            // Walidacja formularza
            if (!subcategoryName) {
                alert('Nazwa podkategorii jest wymagana.');
                return;
            }

            // Znalezienie kategorii nadrzędnej
            const parentCategory = document.querySelector(`.category-item[data-id="${parentCategoryId}"]`);

            if (parentCategory) {
                // Generowanie ID dla nowej podkategorii
                const newSubcategoryId = Math.max(...categoriesData.flatMap(cat => cat.subcategories.map(sub => sub.id)), 0) + 1;
                const subcategorySlug = subcategoryName.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '');

                // Znajdź kategorię nadrzędną w danych i dodaj podkategorię
                const categoryIndex = categoriesData.findIndex(cat => cat.id === parseInt(parentCategoryId));
                if (categoryIndex !== -1) {
                    const newSubcategory = {
                        id: newSubcategoryId,
                        name: subcategoryName,
                        slug: subcategorySlug
                    };

                    categoriesData[categoryIndex].subcategories.push(newSubcategory);

                    // Zapisz do localStorage
                    saveCategoriesData();
                }

                // Tworzenie nowej podkategorii w DOM
                const newSubcategoryHTML = `
                    <li class="subcategory-item" data-id="${newSubcategoryId}">
                        <div class="subcategory-item-header">
                            <span class="subcategory-name">${subcategoryName}</span>
                            <div class="subcategory-actions">
                                <button class="btn-icon edit-subcategory" title="Edytuj podkategorię"><i class="fas fa-edit"></i></button>
                                <button class="btn-icon delete-subcategory" title="Usuń podkategorię"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        </div>
                    </li>
                `;

                // Dodanie nowej podkategorii do kategorii nadrzędnej
                const subcategoriesList = parentCategory.querySelector('.subcategories');
                subcategoriesList.insertAdjacentHTML('beforeend', newSubcategoryHTML);

                // Upewnienie się, że kategoria nadrzędna jest rozwinięta
                parentCategory.classList.add('active');

                // Dodanie obsługi zdarzeń dla nowej podkategorii
                const newSubcategory = subcategoriesList.lastElementChild;

                // Obsługa przycisków akcji
                newSubcategory.querySelector('.edit-subcategory').addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert(`Edycja podkategorii "${subcategoryName}"`);
                });

                newSubcategory.querySelector('.delete-subcategory').addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (confirm(`Czy na pewno chcesz usunąć podkategorię "${subcategoryName}"?`)) {
                        // Usuń podkategorię z danych
                        const categoryIndex = categoriesData.findIndex(cat => cat.id === parseInt(parentCategoryId));
                        if (categoryIndex !== -1) {
                            const subcategoryIndex = categoriesData[categoryIndex].subcategories.findIndex(sub => sub.id === newSubcategoryId);
                            if (subcategoryIndex !== -1) {
                                categoriesData[categoryIndex].subcategories.splice(subcategoryIndex, 1);

                                // Zapisz do localStorage
                                saveCategoriesData();
                            }
                        }

                        newSubcategory.remove();
                        showNotification(`Podkategoria "${subcategoryName}" została usunięta i zsynchronizowana z frontend.`, 'success');
                    }
                });

                // Zamknięcie modalu i wyczyszczenie formularza
                addSubcategoryModal.classList.remove('show');
                addSubcategoryForm.reset();

                // Wyświetlenie powiadomienia
                showNotification(`Podkategoria "${subcategoryName}" została dodana.`, 'success');
            }
        });
    }
}

/**
 * Inicjalizacja selektora ikon
 */
function initIconSelector() {
    const iconSelectors = document.querySelectorAll('.icon-selector');

    iconSelectors.forEach(selector => {
        const selectedIcon = selector.querySelector('.selected-icon');
        const iconOptions = selector.querySelectorAll('.icon-option');

        // Otwieranie/zamykanie dropdown'a
        if (selectedIcon) {
            selectedIcon.addEventListener('click', function() {
                selector.classList.toggle('active');
            });
        }

        // Wybór ikony
        iconOptions.forEach(option => {
            option.addEventListener('click', function() {
                const iconClass = this.getAttribute('data-icon');
                selectedIcon.innerHTML = `<i class="fas ${iconClass}"></i>`;
                selector.classList.remove('active');
            });
        });

        // Zamykanie dropdown'a po kliknięciu poza nim
        document.addEventListener('click', function(e) {
            if (!selector.contains(e.target)) {
                selector.classList.remove('active');
            }
        });
    });
}

/**
 * Otwieranie modalu dodawania podkategorii
 * @param {string} categoryId - ID kategorii nadrzędnej
 * @param {string} categoryName - Nazwa kategorii nadrzędnej
 */
function openAddSubcategoryModal(categoryId, categoryName) {
    const modal = document.getElementById('addSubcategoryModal');

    if (modal) {
        document.getElementById('parent-category-id').value = categoryId;
        document.getElementById('parent-category-name').value = categoryName;
        document.getElementById('new-subcategory-name').value = '';

        modal.classList.add('show');
    }
}

/**
 * Ładowanie danych kategorii do formularza
 * @param {HTMLElement} categoryItem - Element kategorii
 */
function loadCategoryDetails(categoryItem) {
    const categoryId = categoryItem.getAttribute('data-id');
    const categoryName = categoryItem.querySelector('.category-name').textContent;
    const categoryIcon = categoryItem.querySelector('.category-icon').className.split(' ')[1];

    // W rzeczywistej implementacji tutaj byłoby pobieranie danych z API
    // Na potrzeby demonstracji używamy przykładowych danych
    const categoryData = {
        id: categoryId,
        name: categoryName,
        icon: categoryIcon,
        description: categoryId === '1' ? 'Restauracje, kawiarnie, bary i inne lokale gastronomiczne w Żyrardowie.' :
                     categoryId === '2' ? 'Sklepy i punkty handlowe w Żyrardowie.' :
                     categoryId === '3' ? 'Usługi dla mieszkańców i firm w Żyrardowie.' :
                     'Opis kategorii',
        slug: categoryName.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, ''),
        status: true
    };

    // Wypełnienie formularza danymi kategorii
    document.getElementById('category-name-input').value = categoryData.name;
    document.querySelector('.selected-icon').innerHTML = `<i class="fas ${categoryData.icon}"></i>`;
    document.getElementById('category-description').value = categoryData.description;
    document.getElementById('category-slug').value = categoryData.slug;
    document.getElementById('category-status').checked = categoryData.status;
}

/**
 * Wyczyszczenie formularza kategorii
 */
function clearCategoryForm() {
    document.getElementById('category-name-input').value = '';
    document.querySelector('.selected-icon').innerHTML = `<i class="fas fa-list"></i>`;
    document.getElementById('category-description').value = '';
    document.getElementById('category-slug').value = '';
    document.getElementById('category-status').checked = true;
}

/**
 * Funkcja do wyświetlania powiadomień
 * @param {string} message - Treść powiadomienia
 * @param {string} type - Typ powiadomienia (success, info, warning, error)
 */
function showNotification(message, type = 'info') {
    // Tworzenie elementu powiadomienia
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'warning' ? 'fa-exclamation-triangle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // Dodanie do dokumentu
    document.body.appendChild(notification);

    // Dodanie nasłuchiwania zdarzenia do przycisku zamykania
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });

    // Automatyczne usunięcie po 5 sekundach
    setTimeout(() => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);

    // Pokazanie powiadomienia z animacją
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}
