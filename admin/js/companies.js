/**
 * Panel Administracyjny - Żyrardów Poleca
 * JavaScript dla zarządzania firmami z systemem TOP 1-3
 */

document.addEventListener('DOMContentLoaded', function() {
    // Sprawdź autoryzację
    if (!isLoggedIn()) {
        window.location.href = 'index.html';
        return;
    }

    // Inicjalizacja funkcji zarządzania firmami
    initCompaniesManagement();

    // Inicjalizacja systemu TOP 1-3
    initTopPositionsSystem();

    // Załaduj dane firm
    loadCompaniesData();

    // Renderuj tabelę firm
    renderCompaniesTable();
});

/**
 * Inicjalizacja funkcji zarządzania firmami
 */
function initCompaniesManagement() {
    // Obsługa wyszukiwania
    initSearch();

    // Obsługa filtrów
    initFilters();

    // Obsługa zaznaczania firm
    initSelections();

    // Obsługa akcji na firmach
    initActions();

    // Obsługa paginacji
    initPagination();
}

/**
 * Inicjalizacja wyszukiwania
 */
function initSearch() {
    const searchInput = document.getElementById('company-search');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#companies-table-body tr');

            tableRows.forEach(row => {
                const companyName = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const companyAddress = row.querySelector('td:nth-child(5)').textContent.toLowerCase();

                if (companyName.includes(searchTerm) || companyAddress.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
}

/**
 * Inicjalizacja filtrów
 */
function initFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const statusFilter = document.getElementById('status-filter');
    const sortBy = document.getElementById('sort-by');
    const resetFiltersBtn = document.getElementById('reset-filters');

    // Funkcja do filtrowania firm
    function filterCompanies() {
        const categoryValue = categoryFilter.value;
        const statusValue = statusFilter.value;
        const tableRows = document.querySelectorAll('#companies-table-body tr');

        tableRows.forEach(row => {
            const category = row.querySelector('td:nth-child(4)').textContent;
            const statusElement = row.querySelector('.status-badge');
            const status = statusElement ? statusElement.classList.contains('active') ? 'active' :
                           statusElement.classList.contains('pending') ? 'pending' : 'inactive' : '';

            let showRow = true;

            if (categoryValue && !category.includes(categoryValue)) {
                showRow = false;
            }

            if (statusValue && status !== statusValue) {
                showRow = false;
            }

            row.style.display = showRow ? '' : 'none';
        });
    }

    // Funkcja do sortowania firm
    function sortCompanies() {
        const sortValue = sortBy.value;
        const tableBody = document.getElementById('companies-table-body');
        const tableRows = Array.from(tableBody.querySelectorAll('tr'));

        tableRows.sort((a, b) => {
            const aName = a.querySelector('td:nth-child(3)').textContent;
            const bName = b.querySelector('td:nth-child(3)').textContent;
            const aDate = a.querySelector('td:nth-child(7)').textContent;
            const bDate = b.querySelector('td:nth-child(7)').textContent;

            switch (sortValue) {
                case 'name':
                    return aName.localeCompare(bName);
                case 'name-desc':
                    return bName.localeCompare(aName);
                case 'date-newest':
                    return new Date(bDate.split('.').reverse().join('-')) - new Date(aDate.split('.').reverse().join('-'));
                case 'date-oldest':
                    return new Date(aDate.split('.').reverse().join('-')) - new Date(bDate.split('.').reverse().join('-'));
                default:
                    return 0;
            }
        });

        // Usunięcie wszystkich wierszy
        while (tableBody.firstChild) {
            tableBody.removeChild(tableBody.firstChild);
        }

        // Dodanie posortowanych wierszy
        tableRows.forEach(row => {
            tableBody.appendChild(row);
        });
    }

    // Dodanie nasłuchiwania zdarzeń
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterCompanies);
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', filterCompanies);
    }

    if (sortBy) {
        sortBy.addEventListener('change', sortCompanies);
    }

    // Resetowanie filtrów
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            if (categoryFilter) categoryFilter.value = '';
            if (statusFilter) statusFilter.value = '';
            if (sortBy) sortBy.value = 'name';

            const tableRows = document.querySelectorAll('#companies-table-body tr');
            tableRows.forEach(row => {
                row.style.display = '';
            });

            // Sortowanie po nazwie (A-Z)
            sortCompanies();
        });
    }
}

/**
 * Inicjalizacja zaznaczania firm
 */
function initSelections() {
    const selectAll = document.getElementById('select-all');
    const companyCheckboxes = document.querySelectorAll('.company-select');

    if (selectAll) {
        selectAll.addEventListener('change', function() {
            companyCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Aktualizacja stanu "zaznacz wszystkie" gdy zmienia się stan pojedynczych checkboxów
    companyCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(companyCheckboxes).every(cb => cb.checked);
            const anyChecked = Array.from(companyCheckboxes).some(cb => cb.checked);

            if (selectAll) {
                selectAll.checked = allChecked;
                selectAll.indeterminate = anyChecked && !allChecked;
            }
        });
    });
}

/**
 * Inicjalizacja akcji na firmach
 */
function initActions() {
    // Obsługa akcji edycji, podglądu i usuwania
    const actionButtons = document.querySelectorAll('.btn-icon');

    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const companyName = row.querySelector('td:nth-child(3)').textContent;

            if (this.classList.contains('delete')) {
                if (confirm(`Czy na pewno chcesz usunąć firmę "${companyName}"?`)) {
                    // W rzeczywistej implementacji tutaj byłoby usuwanie firmy
                    row.remove();
                    showNotification(`Firma "${companyName}" została usunięta.`, 'success');
                }
            } else if (this.title === 'Edytuj') {
                // W rzeczywistej implementacji tutaj byłoby przekierowanie do edycji firmy
                window.location.href = `companies-add.html?edit=${encodeURIComponent(companyName)}`;
            } else if (this.title === 'Podgląd') {
                // W rzeczywistej implementacji tutaj byłoby przekierowanie do podglądu firmy
                alert(`Podgląd firmy "${companyName}"`);
            }
        });
    });

    // Obsługa akcji zbiorczych
    const applyBulkAction = document.getElementById('apply-bulk-action');
    const bulkAction = document.getElementById('bulk-action');

    if (applyBulkAction && bulkAction) {
        applyBulkAction.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.company-select:checked');
            const action = bulkAction.value;

            if (!action) {
                alert('Wybierz akcję do wykonania.');
                return;
            }

            if (selectedCheckboxes.length === 0) {
                alert('Zaznacz przynajmniej jedną firmę.');
                return;
            }

            const selectedRows = Array.from(selectedCheckboxes).map(checkbox => checkbox.closest('tr'));
            const companyNames = selectedRows.map(row => row.querySelector('td:nth-child(3)').textContent);

            switch (action) {
                case 'activate':
                    if (confirm(`Czy na pewno chcesz aktywować ${selectedRows.length} firm?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge active';
                            statusBadge.textContent = 'Aktywna';
                        });
                        showNotification(`Aktywowano ${selectedRows.length} firm.`, 'success');
                    }
                    break;
                case 'deactivate':
                    if (confirm(`Czy na pewno chcesz dezaktywować ${selectedRows.length} firm?`)) {
                        selectedRows.forEach(row => {
                            const statusBadge = row.querySelector('.status-badge');
                            statusBadge.className = 'status-badge inactive';
                            statusBadge.textContent = 'Nieaktywna';
                        });
                        showNotification(`Dezaktywowano ${selectedRows.length} firm.`, 'success');
                    }
                    break;
                case 'delete':
                    if (confirm(`Czy na pewno chcesz usunąć ${selectedRows.length} firm?`)) {
                        selectedRows.forEach(row => row.remove());
                        showNotification(`Usunięto ${selectedRows.length} firm.`, 'success');
                    }
                    break;
            }

            // Resetowanie zaznaczenia
            const selectAll = document.getElementById('select-all');
            if (selectAll) {
                selectAll.checked = false;
                selectAll.indeterminate = false;
            }

            // Resetowanie akcji
            bulkAction.value = '';
        });
    }
}

/**
 * Inicjalizacja paginacji
 */
function initPagination() {
    const paginationButtons = document.querySelectorAll('.btn-page');

    paginationButtons.forEach(button => {
        if (!button.disabled) {
            button.addEventListener('click', function() {
                // Usunięcie aktywnej klasy z wszystkich przycisków
                paginationButtons.forEach(btn => btn.classList.remove('active'));

                // Dodanie aktywnej klasy do klikniętego przycisku
                this.classList.add('active');

                // W rzeczywistej implementacji tutaj byłoby ładowanie odpowiedniej strony
                console.log('Przejście do strony:', this.textContent);
            });
        }
    });
}

/**
 * Funkcja do wyświetlania powiadomień
 * @param {string} message - Treść powiadomienia
 * @param {string} type - Typ powiadomienia (success, info, warning, error)
 */
function showNotification(message, type = 'info') {
    // Tworzenie elementu powiadomienia
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'warning' ? 'fa-exclamation-triangle' : type === 'error' ? 'fa-times-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;

    // Dodanie do dokumentu
    document.body.appendChild(notification);

    // Dodanie nasłuchiwania zdarzenia do przycisku zamykania
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });

    // Automatyczne usunięcie po 5 sekundach
    setTimeout(() => {
        notification.classList.add('hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);

    // Pokazanie powiadomienia z animacją
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}

/**
 * Inicjalizacja systemu TOP 1-3
 */
function initTopPositionsSystem() {
    // Załaduj dane firm z localStorage
    loadCompaniesData();

    // Dodaj obsługę przycisków TOP w tabeli
    addTopPositionButtons();

    console.log('TOP Positions System initialized');
}

/**
 * Załaduj dane firm z localStorage
 */
function loadCompaniesData() {
    // Sprawdź czy istnieją główne dane firm
    const companiesData = localStorage.getItem('companies_data');
    if (!companiesData) {
        // Inicjalizuj domyślne dane firm
        const defaultCompaniesData = {
            companies: [
                {
                    id: 1,
                    name: 'Restauracja Pod Akacjami',
                    category: 'Jedzenie i Gastronomia',
                    subcategory: 'Restauracje',
                    address: 'ul. Przykładowa 1, Żyrardów',
                    phone: '+**************',
                    email: '<EMAIL>',
                    website: 'www.podakacjami.pl',
                    logo: '../images/business-logo1.jpg',
                    status: 'active',
                    dateAdded: '2023-05-12'
                },
                {
                    id: 2,
                    name: 'Salon Fryzjerski Bella',
                    category: 'Zdrowie i Uroda',
                    subcategory: 'Salony fryzjerskie',
                    address: 'ul. Przykładowa 2, Żyrardów',
                    phone: '+**************',
                    email: '<EMAIL>',
                    website: 'www.bella.pl',
                    logo: '../images/business-logo2.jpg',
                    status: 'active',
                    dateAdded: '2023-06-15'
                },
                {
                    id: 3,
                    name: 'Sklep Sportowy Active',
                    category: 'Zakupy i Handel',
                    subcategory: 'Odzież i obuwie',
                    address: 'ul. Przykładowa 3, Żyrardów',
                    phone: '+**************',
                    email: '<EMAIL>',
                    website: 'www.active.pl',
                    logo: '../images/business-logo3.jpg',
                    status: 'pending',
                    dateAdded: '2023-07-20'
                },
                {
                    id: 4,
                    name: 'Kancelaria Prawna Paragraf',
                    category: 'Usługi Biznesowe',
                    subcategory: 'Doradztwo prawne',
                    address: 'ul. Przykładowa 4, Żyrardów',
                    phone: '+**************',
                    email: '<EMAIL>',
                    website: 'www.paragraf.pl',
                    logo: '../images/business-logo1.jpg',
                    status: 'active',
                    dateAdded: '2023-08-10'
                }
            ]
        };
        localStorage.setItem('companies_data', JSON.stringify(defaultCompaniesData));
    }

    // Sprawdź dane TOP firm
    const topData = localStorage.getItem('companies_top_data');
    if (!topData) {
        // Inicjalizuj domyślne dane TOP firm
        const defaultTopData = {
            companies: [
                {
                    id: 1,
                    name: 'Restauracja Pod Akacjami',
                    category: 'Jedzenie i Gastronomia',
                    subcategory: 'Restauracje',
                    topPosition: 1,
                    status: 'active'
                },
                {
                    id: 2,
                    name: 'Salon Fryzjerski Bella',
                    category: 'Zdrowie i Uroda',
                    subcategory: 'Salony fryzjerskie',
                    topPosition: 2,
                    status: 'active'
                },
                {
                    id: 3,
                    name: 'Sklep Sportowy Active',
                    category: 'Zakupy i Handel',
                    subcategory: 'Odzież i obuwie',
                    topPosition: null,
                    status: 'pending'
                },
                {
                    id: 4,
                    name: 'Kancelaria Prawna Paragraf',
                    category: 'Usługi Biznesowe',
                    subcategory: 'Doradztwo prawne',
                    topPosition: 3,
                    status: 'active'
                }
            ]
        };
        localStorage.setItem('companies_top_data', JSON.stringify(defaultTopData));
    }
}

/**
 * Renderuj tabelę firm
 */
function renderCompaniesTable() {
    const tableBody = document.getElementById('companies-table-body');
    if (!tableBody) return;

    try {
        // Pobierz dane firm
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        const topData = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');

        // Wyczyść tabelę
        tableBody.innerHTML = '';

        // Renderuj każdą firmę
        companiesData.companies.forEach(company => {
            const topCompany = topData.companies.find(tc => tc.id === company.id);
            const topPosition = topCompany?.topPosition || null;

            const row = document.createElement('tr');
            row.dataset.companyId = company.id;

            row.innerHTML = `
                <td><input type="checkbox" class="company-select" data-company-id="${company.id}"></td>
                <td><img src="${company.logo || '../images/business-logo1.jpg'}" alt="Logo firmy" class="company-logo"></td>
                <td>
                    ${company.name}
                    ${topPosition ? `<span class="top-badge top-${topPosition}">TOP ${topPosition}</span>` : ''}
                </td>
                <td>${company.category}</td>
                <td>${company.address}</td>
                <td><span class="status-badge ${company.status}">${getStatusText(company.status)}</span></td>
                <td>${formatDate(company.dateAdded)}</td>
                <td class="actions">
                    <button class="btn-icon edit-company" title="Edytuj" data-company-id="${company.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon view-company" title="Podgląd" data-company-id="${company.id}">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon top-position" title="Pozycja TOP" data-company-id="${company.id}">
                        <i class="fas fa-star"></i>
                    </button>
                    <button class="btn-icon toggle-status" title="Zmień status" data-company-id="${company.id}">
                        <i class="fas fa-toggle-${company.status === 'active' ? 'on' : 'off'}"></i>
                    </button>
                    <button class="btn-icon delete-company" title="Usuń" data-company-id="${company.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            tableBody.appendChild(row);
        });

        // Dodaj obsługę zdarzeń dla przycisków
        addTableEventListeners();

    } catch (error) {
        console.error('Błąd podczas renderowania tabeli firm:', error);
        tableBody.innerHTML = '<tr><td colspan="8">Błąd podczas ładowania danych firm</td></tr>';
    }
}

/**
 * Dodaj obsługę zdarzeń dla przycisków w tabeli
 */
function addTableEventListeners() {
    // Edycja firmy
    document.querySelectorAll('.edit-company').forEach(btn => {
        btn.addEventListener('click', function() {
            const companyId = this.dataset.companyId;
            editCompany(companyId);
        });
    });

    // Podgląd firmy
    document.querySelectorAll('.view-company').forEach(btn => {
        btn.addEventListener('click', function() {
            const companyId = this.dataset.companyId;
            viewCompany(companyId);
        });
    });

    // Pozycja TOP
    document.querySelectorAll('.top-position').forEach(btn => {
        btn.addEventListener('click', function() {
            const companyId = this.dataset.companyId;
            const companyName = this.closest('tr').querySelector('td:nth-child(3)').textContent.trim();
            manageTopPosition(companyId, companyName);
        });
    });

    // Zmiana statusu
    document.querySelectorAll('.toggle-status').forEach(btn => {
        btn.addEventListener('click', function() {
            const companyId = this.dataset.companyId;
            toggleCompanyStatus(companyId);
        });
    });

    // Usuwanie firmy
    document.querySelectorAll('.delete-company').forEach(btn => {
        btn.addEventListener('click', function() {
            const companyId = this.dataset.companyId;
            const companyName = this.closest('tr').querySelector('td:nth-child(3)').textContent.trim();
            deleteCompany(companyId, companyName);
        });
    });
}

/**
 * Formatuj datę
 */
function formatDate(dateString) {
    if (!dateString) return 'Brak daty';
    const date = new Date(dateString);
    return date.toLocaleDateString('pl-PL');
}

/**
 * Pobierz tekst statusu
 */
function getStatusText(status) {
    const statusMap = {
        'active': 'Aktywna',
        'pending': 'Oczekująca',
        'inactive': 'Nieaktywna',
        'suspended': 'Zawieszona'
    };
    return statusMap[status] || status;
}

/**
 * Edytuj firmę
 */
function editCompany(companyId) {
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        const company = companiesData.companies.find(c => c.id == companyId);

        if (company) {
            // Zapisz dane firmy do edycji
            localStorage.setItem('editCompanyData', JSON.stringify(company));
            // Przekieruj do strony edycji
            window.location.href = `companies-add.html?edit=${companyId}`;
        } else {
            showNotification('Nie znaleziono firmy do edycji', 'error');
        }
    } catch (error) {
        console.error('Błąd podczas edycji firmy:', error);
        showNotification('Wystąpił błąd podczas edycji firmy', 'error');
    }
}

/**
 * Podgląd firmy
 */
function viewCompany(companyId) {
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        const company = companiesData.companies.find(c => c.id == companyId);

        if (company) {
            // Otwórz modal z podglądem firmy
            showCompanyPreviewModal(company);
        } else {
            showNotification('Nie znaleziono firmy', 'error');
        }
    } catch (error) {
        console.error('Błąd podczas podglądu firmy:', error);
        showNotification('Wystąpił błąd podczas podglądu firmy', 'error');
    }
}

/**
 * Zmień status firmy
 */
function toggleCompanyStatus(companyId) {
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        const company = companiesData.companies.find(c => c.id == companyId);

        if (company) {
            // Zmień status
            company.status = company.status === 'active' ? 'pending' : 'active';

            // Zapisz zmiany
            localStorage.setItem('companies_data', JSON.stringify(companiesData));

            // Aktualizuj wyświetlanie
            renderCompaniesTable();

            // Synchronizuj z frontend
            syncCompaniesWithFrontend();

            showNotification(`Status firmy "${company.name}" został zmieniony na ${getStatusText(company.status)}`, 'success');
        }
    } catch (error) {
        console.error('Błąd podczas zmiany statusu firmy:', error);
        showNotification('Wystąpił błąd podczas zmiany statusu firmy', 'error');
    }
}

/**
 * Usuń firmę
 */
function deleteCompany(companyId, companyName) {
    if (!confirm(`Czy na pewno chcesz usunąć firmę "${companyName}"?`)) {
        return;
    }

    try {
        // Usuń z głównych danych
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        companiesData.companies = companiesData.companies.filter(c => c.id != companyId);
        localStorage.setItem('companies_data', JSON.stringify(companiesData));

        // Usuń z danych TOP
        const topData = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');
        topData.companies = topData.companies.filter(c => c.id != companyId);
        localStorage.setItem('companies_top_data', JSON.stringify(topData));

        // Aktualizuj wyświetlanie
        renderCompaniesTable();

        // Synchronizuj z frontend
        syncCompaniesWithFrontend();
        updateFrontendTopCompanies(topData.companies);

        showNotification(`Firma "${companyName}" została usunięta`, 'success');

    } catch (error) {
        console.error('Błąd podczas usuwania firmy:', error);
        showNotification('Wystąpił błąd podczas usuwania firmy', 'error');
    }
}

/**
 * Synchronizuj firmy z frontend
 */
function syncCompaniesWithFrontend() {
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');

        // Przygotuj dane dla frontend
        const frontendData = companiesData.companies.filter(c => c.status === 'active').map(company => ({
            id: company.id,
            name: company.name,
            category: company.category,
            subcategory: company.subcategory,
            description: company.description,
            address: company.address,
            phone: company.phone,
            email: company.email,
            website: company.website,
            logo: company.logo || '../images/business-logo1.jpg',
            openingHours: company.openingHours
        }));

        // Zapisz dane dla frontend
        localStorage.setItem('frontend_companies', JSON.stringify(frontendData));

        // Wyślij event o aktualizacji
        window.dispatchEvent(new CustomEvent('companiesUpdated', {
            detail: { companies: frontendData }
        }));

    } catch (error) {
        console.error('Błąd podczas synchronizacji z frontend:', error);
    }
}

/**
 * Wyświetl powiadomienie
 */
function showNotification(message, type = 'info') {
    // Usuń istniejące powiadomienia
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());

    // Utwórz nowe powiadomienie
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">&times;</button>
    `;

    // Dodaj style
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 15px;
        z-index: 10000;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Obsługa zamykania
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => notification.remove());

    // Auto-usuwanie po 5 sekundach
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Dodaj przyciski TOP do tabeli firm
 */
function addTopPositionButtons() {
    const tableRows = document.querySelectorAll('#companies-table-body tr');

    tableRows.forEach((row, index) => {
        const actionsCell = row.querySelector('.actions');
        if (actionsCell) {
            // Dodaj przycisk zarządzania pozycjami TOP
            const topButton = document.createElement('button');
            topButton.className = 'btn-icon manage-top';
            topButton.title = 'Zarządzaj pozycją TOP';
            topButton.innerHTML = '<i class="fas fa-trophy"></i>';
            topButton.dataset.companyId = index + 1;

            // Wstaw przed przyciskiem usuwania
            const deleteButton = actionsCell.querySelector('.delete');
            actionsCell.insertBefore(topButton, deleteButton);

            // Dodaj obsługę kliknięcia
            topButton.addEventListener('click', function() {
                const companyId = this.dataset.companyId;
                const companyName = row.querySelector('td:nth-child(3)').textContent;
                manageTopPosition(companyId, companyName);
            });
        }
    });
}

/**
 * Zarządzanie pozycją TOP firmy
 */
function manageTopPosition(companyId, companyName) {
    const data = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');
    const company = data.companies.find(c => c.id == companyId);

    if (!company) {
        // Dodaj firmę do danych jeśli nie istnieje
        const newCompany = {
            id: parseInt(companyId),
            name: companyName,
            category: 'Jedzenie i Gastronomia', // Domyślna kategoria
            subcategory: 'Restauracje', // Domyślna podkategoria
            topPosition: null,
            status: 'active'
        };
        data.companies.push(newCompany);
        localStorage.setItem('companies_top_data', JSON.stringify(data));
    }

    showTopPositionModal(companyId, companyName);
}

/**
 * Pokaż modal zarządzania pozycją TOP
 */
function showTopPositionModal(companyId, companyName) {
    const data = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');
    const company = data.companies.find(c => c.id == companyId);

    if (!company) return;

    const currentPosition = company.topPosition ? `TOP ${company.topPosition}` : 'Brak pozycji TOP';
    const subcategory = company.subcategory || 'Restauracje';

    // Sprawdź jakie pozycje są zajęte w tej podkategorii
    const occupiedPositions = data.companies
        .filter(c => c.subcategory === subcategory && c.id != companyId && c.topPosition)
        .map(c => c.topPosition);

    let options = ['Brak pozycji TOP'];
    for (let i = 1; i <= 3; i++) {
        if (occupiedPositions.includes(i)) {
            const occupyingCompany = data.companies.find(c => c.subcategory === subcategory && c.topPosition === i);
            options.push(`TOP ${i} (zajęte przez: ${occupyingCompany.name})`);
        } else {
            options.push(`TOP ${i} (dostępne)`);
        }
    }

    const message = `Firma: ${companyName}\nPodkategoria: ${subcategory}\nAktualna pozycja: ${currentPosition}\n\nWybierz nową pozycję:\n${options.map((opt, idx) => `${idx}: ${opt}`).join('\n')}`;

    const choice = prompt(message + '\n\nWpisz numer opcji (0-3):');

    if (choice !== null) {
        const choiceNum = parseInt(choice);
        if (choiceNum >= 0 && choiceNum <= 3) {
            updateTopPosition(companyId, choiceNum === 0 ? null : choiceNum, subcategory);
        }
    }
}

/**
 * Aktualizuj pozycję TOP firmy
 */
function updateTopPosition(companyId, newPosition, subcategory) {
    const data = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');
    const companyIndex = data.companies.findIndex(c => c.id == companyId);

    if (companyIndex === -1) return;

    const company = data.companies[companyIndex];

    if (newPosition && newPosition >= 1 && newPosition <= 3) {
        // Sprawdź czy pozycja jest już zajęta
        const conflictingCompany = data.companies.find(c =>
            c.id != companyId &&
            c.subcategory === subcategory &&
            c.topPosition === newPosition
        );

        if (conflictingCompany) {
            if (confirm(`Pozycja TOP ${newPosition} w podkategorii "${subcategory}" jest już zajęta przez firmę "${conflictingCompany.name}". Czy chcesz ją zastąpić?`)) {
                // Usuń pozycję TOP z konfliktowej firmy
                conflictingCompany.topPosition = null;
            } else {
                return;
            }
        }

        company.topPosition = newPosition;
    } else {
        company.topPosition = null;
    }

    // Zapisz zmiany
    localStorage.setItem('companies_top_data', JSON.stringify(data));

    // Aktualizuj wyświetlanie w tabeli
    renderCompaniesTable();

    // Aktualizuj frontend
    updateFrontendTopCompanies(data.companies);

    const positionText = company.topPosition ? `TOP ${company.topPosition}` : 'usunięta';
    showNotification(`Pozycja ${positionText} została przydzielona firmie "${company.name}"`, 'success');
}

/**
 * Aktualizuj wyświetlanie w tabeli
 */
function updateTableDisplay(companyId, topPosition) {
    const tableRows = document.querySelectorAll('#companies-table-body tr');
    const targetRow = Array.from(tableRows)[companyId - 1]; // Zakładając że ID odpowiada indeksowi + 1

    if (targetRow) {
        const nameCell = targetRow.querySelector('td:nth-child(3)');
        const companyName = nameCell.textContent.trim();

        // Usuń istniejące badge TOP
        const existingBadge = nameCell.querySelector('.top-badge');
        if (existingBadge) {
            existingBadge.remove();
        }

        // Dodaj nowy badge TOP jeśli pozycja została przydzielona
        if (topPosition) {
            const topBadge = document.createElement('span');
            topBadge.className = `top-badge top-${topPosition}`;
            topBadge.textContent = `TOP ${topPosition}`;
            nameCell.appendChild(topBadge);
        }
    }
}

/**
 * Aktualizuj firmy TOP na frontend
 */
function updateFrontendTopCompanies(companies) {
    // Grupuj firmy według podkategorii
    const topCompaniesBySubcategory = {};

    companies.forEach(company => {
        if (company.topPosition && company.status === 'active') {
            if (!topCompaniesBySubcategory[company.subcategory]) {
                topCompaniesBySubcategory[company.subcategory] = {};
            }
            topCompaniesBySubcategory[company.subcategory][company.topPosition] = company;
        }
    });

    // Zapisz dane TOP firm dla frontend
    localStorage.setItem('frontend_top_companies', JSON.stringify(topCompaniesBySubcategory));

    // Wyślij event o zmianie TOP firm
    window.dispatchEvent(new CustomEvent('topCompaniesUpdated', {
        detail: topCompaniesBySubcategory
    }));

    console.log('Frontend TOP companies updated:', topCompaniesBySubcategory);
}

/**
 * Pokaż modal ustawiania pozycji TOP
 */
function showTopPositionModal(companyId) {
    const modal = document.getElementById('topPositionModal');
    const companyNameElement = document.getElementById('topPositionCompanyName');

    if (!modal || !companyNameElement) return;

    // Znajdź firmę w danych
    const company = findCompanyById(companyId);
    if (!company) return;

    // Ustaw nazwę firmy
    companyNameElement.textContent = company.name;

    // Załaduj aktualne pozycje TOP
    loadCurrentTopPositionsModal();

    // Pokaż modal
    modal.style.display = 'block';
    modal.dataset.companyId = companyId;
}

/**
 * Wybierz pozycję TOP
 */
function selectTopPosition(optionElement) {
    // Usuń poprzednie zaznaczenie
    document.querySelectorAll('.top-position-option').forEach(option => {
        option.classList.remove('selected');
    });

    // Zaznacz wybraną pozycję
    optionElement.classList.add('selected');

    // Zapisz wybraną pozycję
    window.selectedTopPosition = optionElement.dataset.position;
}

/**
 * Zapisz pozycję TOP
 */
async function saveTopPosition() {
    const modal = document.getElementById('topPositionModal');
    const companyId = modal.dataset.companyId;
    const position = window.selectedTopPosition;

    if (!companyId || !position) {
        showNotification('Wybierz pozycję TOP', 'error');
        return;
    }

    try {
        // Symulacja API - w rzeczywistości użyj prawdziwego API
        const topData = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');

        // Usuń pozycję z innych firm jeśli ustawiamy nową
        if (position !== 'remove') {
            topData.companies.forEach(company => {
                if (company.topPosition == position) {
                    company.topPosition = null;
                }
            });
        }

        // Ustaw nową pozycję
        const company = topData.companies.find(c => c.id == companyId);
        if (company) {
            company.topPosition = position === 'remove' ? null : parseInt(position);
        }

        // Zapisz zmiany
        localStorage.setItem('companies_top_data', JSON.stringify(topData));

        showNotification('Pozycja TOP została zaktualizowana', 'success');
        closeModal('topPositionModal');
        renderCompaniesTable(); // Odśwież tabelę

    } catch (error) {
        console.error('Błąd zapisywania pozycji TOP:', error);
        showNotification('Błąd zapisywania pozycji TOP', 'error');
    }
}

/**
 * Załaduj aktualne pozycje TOP do modalu
 */
function loadCurrentTopPositionsModal() {
    try {
        const topData = JSON.parse(localStorage.getItem('companies_top_data') || '{"companies":[]}');

        // Wyczyść aktualne pozycje
        ['currentTop1', 'currentTop2', 'currentTop3'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = '<span class="empty">Wolne</span>';
            }
        });

        // Ustaw aktualne pozycje
        topData.companies.forEach(company => {
            if (company.topPosition) {
                const element = document.getElementById(`currentTop${company.topPosition}`);
                if (element) {
                    element.innerHTML = `<span class="occupied">${company.name}</span>`;
                }
            }
        });
    } catch (error) {
        console.error('Błąd ładowania pozycji TOP:', error);
    }
}

/**
 * Znajdź firmę po ID
 */
function findCompanyById(companyId) {
    try {
        const companiesData = JSON.parse(localStorage.getItem('companies_data') || '{"companies":[]}');
        return companiesData.companies.find(company => company.id == companyId);
    } catch (error) {
        console.error('Błąd znajdowania firmy:', error);
        return null;
    }
}

/**
 * Zamknij modal
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';

        // Wyczyść zaznaczenia
        modal.querySelectorAll('.top-position-option').forEach(option => {
            option.classList.remove('selected');
        });

        window.selectedTopPosition = null;
    }
}

// Eksportuj funkcje dla innych modułów
window.topPositionsManager = {
    updateTopPosition,
    updateFrontendTopCompanies,
    loadCompaniesData,
    showTopPositionModal,
    selectTopPosition,
    saveTopPosition,
    closeModal
};
