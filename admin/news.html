<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zarządzanie wiadomościami - Panel Administracyjny</title>
    <meta name="description" content="Panel administracyjny - zarządzanie wiadomościami lokalnymi">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/news.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
    <!-- HugeRTE WYSIWYG Editor (darmowa wersja TinyMCE v6) -->
    <script src="https://cdn.jsdelivr.net/npm/@hugerte/hugerte@6/dist/hugerte.min.js"></script>
</head>
<body class="dashboard-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="../images/logo.png" alt="Żyrardów Poleca">
                    <span>Panel Admin</span>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tags"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li>
                        <a href="menus.html">
                            <i class="fas fa-bars"></i>
                            <span>Menu</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <div class="header-left">
                    <h1>Zarządzanie wiadomościami</h1>
                    <p class="page-description">Dodawaj, edytuj i zarządzaj wiadomościami lokalnymi</p>
                </div>
                <div class="header-actions">
                    <button id="addNewsBtn" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Dodaj wiadomość
                    </button>
                    <button id="importNewsBtn" class="btn btn-outline">
                        <i class="fas fa-file-import"></i>
                        Importuj z szablonu
                    </button>
                </div>
            </div>

            <div class="content-body">
                <!-- Filtry i wyszukiwarka -->
                <div class="news-filters">
                    <div class="filters-row">
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" id="newsSearch" placeholder="Szukaj wiadomości...">
                        </div>
                        <div class="filter-group">
                            <select id="statusFilter">
                                <option value="all">Wszystkie statusy</option>
                                <option value="published">Opublikowane</option>
                                <option value="draft">Szkice</option>
                                <option value="archived">Zarchiwizowane</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <select id="categoryFilter">
                                <option value="all">Wszystkie kategorie</option>
                                <option value="local">Wiadomości lokalne</option>
                                <option value="events">Wydarzenia</option>
                                <option value="business">Biznes</option>
                                <option value="culture">Kultura</option>
                                <option value="sport">Sport</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Lista wiadomości -->
                <div class="news-container">
                    <div class="news-list" id="newsList">
                        <!-- Wiadomości będą ładowane dynamicznie -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal dodawania/edycji wiadomości -->
    <div class="modal" id="newsModal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="modalTitle">Dodaj nową wiadomość</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="newsForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="newsTitle">Tytuł wiadomości *</label>
                            <input type="text" id="newsTitle" name="title" required>
                        </div>
                        <div class="form-group">
                            <label for="newsCategory">Kategoria *</label>
                            <select id="newsCategory" name="category" required>
                                <option value="">Wybierz kategorię</option>
                                <option value="local">Wiadomości lokalne</option>
                                <option value="events">Wydarzenia</option>
                                <option value="business">Biznes</option>
                                <option value="culture">Kultura</option>
                                <option value="sport">Sport</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="newsExcerpt">Krótki opis (excerpt) *</label>
                        <textarea id="newsExcerpt" name="excerpt" rows="3" required placeholder="Krótki opis wiadomości wyświetlany na liście..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="newsContent">Treść wiadomości *</label>
                        <textarea id="newsContent" name="content" rows="10" required placeholder="Pełna treść wiadomości..."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="newsImage">Zdjęcie główne</label>
                            <input type="file" id="newsImage" name="image" accept="image/*">
                            <div class="image-preview" id="imagePreview"></div>
                        </div>
                        <div class="form-group">
                            <label for="newsImageAlt">Opis alternatywny zdjęcia</label>
                            <input type="text" id="newsImageAlt" name="imageAlt" placeholder="Opis zdjęcia dla SEO">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="newsStatus">Status</label>
                            <select id="newsStatus" name="status">
                                <option value="draft">Szkic</option>
                                <option value="published">Opublikowane</option>
                                <option value="archived">Zarchiwizowane</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="newsDate">Data publikacji</label>
                            <input type="datetime-local" id="newsDate" name="publishDate">
                        </div>
                    </div>

                    <!-- SEO -->
                    <div class="seo-section">
                        <h3>Ustawienia SEO</h3>
                        <div class="form-group">
                            <label for="newsSlug">URL (slug)</label>
                            <input type="text" id="newsSlug" name="slug" placeholder="automatycznie-generowany-url">
                        </div>
                        <div class="form-group">
                            <label for="newsMetaDescription">Meta opis</label>
                            <textarea id="newsMetaDescription" name="metaDescription" rows="2" placeholder="Opis dla wyszukiwarek (max 160 znaków)"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="newsTags">Tagi (oddzielone przecinkami)</label>
                            <input type="text" id="newsTags" name="tags" placeholder="żyrardów, wiadomości, lokalne">
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Zapisz wiadomość</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal importu z szablonu -->
    <div class="modal" id="importModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Import wiadomości z szablonu</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="import-info">
                    <p>Wklej dane w formacie JSON wygenerowane przez AI:</p>
                </div>
                <form id="importForm">
                    <div class="form-group">
                        <label for="importData">Dane JSON</label>
                        <textarea id="importData" name="importData" rows="15" placeholder='{"title": "Tytuł wiadomości", "category": "local", ...}'></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline modal-cancel">Anuluj</button>
                        <button type="submit" class="btn btn-primary">Importuj wiadomość</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/news.js"></script>
</body>
</html>
