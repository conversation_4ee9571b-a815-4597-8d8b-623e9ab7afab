<?php
/**
 * API dla zarządzania firmami z pozycjami TOP
 * Żyrardów Poleca - Panel Administracyjny
 * System plików JSON
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ścieżki do plików danych
$dataDir = __DIR__ . '/../data/';
$companiesFile = $dataDir . 'companies.json';
$categoriesFile = $dataDir . 'categories.json';

// Utwórz katalog danych jeśli nie istnieje
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Funkcje pomocnicze
function loadData($file) {
    if (!file_exists($file)) {
        return [];
    }
    $content = file_get_contents($file);
    return $content ? json_decode($content, true) : [];
}

function saveData($file, $data) {
    return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function getNextId($data) {
    if (empty($data)) return 1;
    $ids = array_column($data, 'id');
    return max($ids) + 1;
}

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'top') {
            getTopCompanies();
        } elseif ($path === 'all') {
            getAllCompanies();
        } elseif ($path === 'categories') {
            getCategories();
        } else {
            getCompanies();
        }
        break;

    case 'POST':
        if ($path === 'create') {
            createCompany();
        } elseif ($path === 'set-top-position') {
            setTopPosition();
        }
        break;

    case 'PUT':
        if ($path === 'update') {
            updateCompany();
        }
        break;

    case 'DELETE':
        if ($path === 'delete') {
            deleteCompany();
        }
        break;

    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz TOP 3 firmy
 */
function getTopCompanies() {
    global $companiesFile, $categoriesFile;

    try {
        $companies = loadData($companiesFile);
        $categories = loadData($categoriesFile);

        // Filtruj firmy TOP i aktywne
        $topCompanies = array_filter($companies, function($company) {
            return isset($company['status']) && $company['status'] === 'active'
                   && isset($company['topPosition']) && $company['topPosition'] !== null;
        });

        // Sortuj według pozycji TOP
        usort($topCompanies, function($a, $b) {
            return $a['topPosition'] <=> $b['topPosition'];
        });

        // Ogranicz do 3 firm
        $topCompanies = array_slice($topCompanies, 0, 3);

        // Dodaj nazwy kategorii
        foreach ($topCompanies as &$company) {
            if (isset($company['categoryId'])) {
                $category = array_filter($categories, function($cat) use ($company) {
                    return $cat['id'] == $company['categoryId'];
                });
                if (!empty($category)) {
                    $category = array_values($category)[0];
                    $company['category_name'] = $category['name'] ?? '';
                    $company['category_icon'] = $category['icon'] ?? '';
                }
            }
        }

        echo json_encode([
            'success' => true,
            'data' => array_values($topCompanies)
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania TOP firm',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie firmy z paginacją i filtrami
 */
function getAllCompanies($pdo) {
    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';
        $categoryId = $_GET['categoryId'] ?? '';
        $search = $_GET['search'] ?? '';

        $offset = ($page - 1) * $limit;

        // Budowanie zapytania WHERE
        $whereConditions = [];
        $params = [];

        if ($status) {
            $whereConditions[] = "c.status = :status";
            $params['status'] = $status;
        }

        if ($categoryId) {
            $whereConditions[] = "c.categoryId = :categoryId";
            $params['categoryId'] = $categoryId;
        }

        if ($search) {
            $whereConditions[] = "(c.name LIKE :search OR c.address LIKE :search)";
            $params['search'] = "%{$search}%";
        }

        $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // Zapytanie główne
        $stmt = $pdo->prepare("
            SELECT c.*, cat.name as category_name, cat.icon as category_icon
            FROM companies c
            LEFT JOIN categories cat ON c.categoryId = cat.id
            {$whereClause}
            ORDER BY c.topPosition ASC, c.name ASC
            LIMIT :limit OFFSET :offset
        ");

        foreach ($params as $key => $value) {
            $stmt->bindValue(":{$key}", $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        $companies = $stmt->fetchAll();

        // Zapytanie dla liczby wszystkich rekordów
        $countStmt = $pdo->prepare("
            SELECT COUNT(*) as total
            FROM companies c
            {$whereClause}
        ");

        foreach ($params as $key => $value) {
            $countStmt->bindValue(":{$key}", $value);
        }
        $countStmt->execute();
        $total = $countStmt->fetch()['total'];

        echo json_encode([
            'success' => true,
            'data' => [
                'companies' => $companies,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania firm',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz kategorie
 */
function getCategories($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT id, name, slug, icon, color, parentId
            FROM categories
            WHERE parentId IS NULL
            ORDER BY sortOrder ASC, name ASC
        ");
        $stmt->execute();
        $categories = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'data' => $categories
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Ustaw pozycję TOP dla firmy
 */
function setTopPosition() {
    global $companiesFile;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $companyId = $input['companyId'] ?? null;
        $topPosition = $input['topPosition'] ?? null;

        if (!$companyId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID firmy jest wymagane'
            ]);
            return;
        }

        $companies = loadData($companiesFile);

        // Jeśli ustawiamy pozycję TOP, usuń ją z innych firm
        if ($topPosition !== null && $topPosition >= 1 && $topPosition <= 3) {
            foreach ($companies as &$company) {
                if (isset($company['topPosition']) && $company['topPosition'] == $topPosition) {
                    $company['topPosition'] = null;
                }
            }
        }

        // Znajdź firmę i ustaw nową pozycję
        $companyFound = false;
        foreach ($companies as &$company) {
            if ($company['id'] == $companyId) {
                $company['topPosition'] = $topPosition;
                $companyFound = true;
                break;
            }
        }

        if (!$companyFound) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Firma nie została znaleziona'
            ]);
            return;
        }

        // Zapisz zmiany
        if (saveData($companiesFile, $companies)) {
            echo json_encode([
                'success' => true,
                'message' => 'Pozycja TOP została zaktualizowana'
            ]);
        } else {
            throw new Exception('Nie udało się zapisać danych');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd aktualizacji pozycji TOP',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Utwórz nową firmę
 */
function createCompany($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $requiredFields = ['name', 'categoryId'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Pole {$field} jest wymagane"
                ]);
                return;
            }
        }

        // Generuj slug
        $slug = generateSlug($input['name']);

        $stmt = $pdo->prepare("
            INSERT INTO companies (name, slug, description, categoryId, subcategoryId, address, postalCode, city, phone, email, website, logo, status, publishedAt)
            VALUES (:name, :slug, :description, :categoryId, :subcategoryId, :address, :postalCode, :city, :phone, :email, :website, :logo, :status, NOW())
        ");

        $stmt->execute([
            'name' => $input['name'],
            'slug' => $slug,
            'description' => $input['description'] ?? null,
            'categoryId' => $input['categoryId'],
            'subcategoryId' => $input['subcategoryId'] ?? null,
            'address' => $input['address'] ?? null,
            'postalCode' => $input['postalCode'] ?? null,
            'city' => $input['city'] ?? 'Żyrardów',
            'phone' => $input['phone'] ?? null,
            'email' => $input['email'] ?? null,
            'website' => $input['website'] ?? null,
            'logo' => $input['logo'] ?? null,
            'status' => $input['status'] ?? 'pending'
        ]);

        $companyId = $pdo->lastInsertId();

        echo json_encode([
            'success' => true,
            'message' => 'Firma została utworzona pomyślnie',
            'data' => ['id' => $companyId]
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd tworzenia firmy',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Generuj slug z nazwy
 */
function generateSlug($name) {
    $slug = strtolower($name);
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    $slug = trim($slug, '-');
    return $slug;
}
?>
