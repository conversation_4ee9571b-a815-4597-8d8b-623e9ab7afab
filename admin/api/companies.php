<?php
/**
 * API dla zarządzania firmami z pozycjami TOP
 * Żyrardów Poleca - Panel Administracyjny
 * System plików JSON
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ścieżki do plików danych
$dataDir = __DIR__ . '/../data/';
$companiesFile = $dataDir . 'companies.json';
$categoriesFile = $dataDir . 'categories.json';

// Utwórz katalog danych jeśli nie istnieje
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Funkcje pomocnicze
function loadData($file) {
    if (!file_exists($file)) {
        return [];
    }
    $content = file_get_contents($file);
    return $content ? json_decode($content, true) : [];
}

function saveData($file, $data) {
    return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function getNextId($data) {
    if (empty($data)) return 1;
    $ids = array_column($data, 'id');
    return max($ids) + 1;
}

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'top') {
            getTopCompanies();
        } elseif ($path === 'all') {
            getAllCompanies();
        } elseif ($path === 'categories') {
            getCategories();
        } else {
            getAllCompanies();
        }
        break;

    case 'POST':
        if ($path === 'create') {
            createCompany();
        } elseif ($path === 'set-top-position') {
            setTopPosition();
        }
        break;

    case 'PUT':
        if ($path === 'update') {
            updateCompany();
        }
        break;

    case 'DELETE':
        if ($path === 'delete') {
            deleteCompany();
        }
        break;

    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz TOP 3 firmy
 */
function getTopCompanies() {
    global $companiesFile, $categoriesFile;

    try {
        $companies = loadData($companiesFile);
        $categories = loadData($categoriesFile);

        // Filtruj firmy TOP i aktywne
        $topCompanies = array_filter($companies, function($company) {
            return isset($company['status']) && $company['status'] === 'active'
                   && isset($company['topPosition']) && $company['topPosition'] !== null;
        });

        // Sortuj według pozycji TOP
        usort($topCompanies, function($a, $b) {
            return $a['topPosition'] <=> $b['topPosition'];
        });

        // Ogranicz do 3 firm
        $topCompanies = array_slice($topCompanies, 0, 3);

        // Dodaj nazwy kategorii
        foreach ($topCompanies as &$company) {
            if (isset($company['categoryId'])) {
                $category = array_filter($categories, function($cat) use ($company) {
                    return $cat['id'] == $company['categoryId'];
                });
                if (!empty($category)) {
                    $category = array_values($category)[0];
                    $company['category_name'] = $category['name'] ?? '';
                    $company['category_icon'] = $category['icon'] ?? '';
                }
            }
        }

        echo json_encode([
            'success' => true,
            'data' => array_values($topCompanies)
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania TOP firm',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie firmy z paginacją i filtrami
 */
function getAllCompanies() {
    global $companiesFile, $categoriesFile;

    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';
        $categoryId = $_GET['categoryId'] ?? '';
        $search = $_GET['search'] ?? '';

        $companies = loadData($companiesFile);
        $categories = loadData($categoriesFile);

        // Filtrowanie
        $filteredCompanies = array_filter($companies, function($company) use ($status, $categoryId, $search) {
            // Filtr statusu
            if ($status && (!isset($company['status']) || $company['status'] !== $status)) {
                return false;
            }

            // Filtr kategorii
            if ($categoryId && (!isset($company['categoryId']) || $company['categoryId'] != $categoryId)) {
                return false;
            }

            // Filtr wyszukiwania
            if ($search) {
                $searchLower = strtolower($search);
                $nameMatch = isset($company['name']) && strpos(strtolower($company['name']), $searchLower) !== false;
                $addressMatch = isset($company['address']) && strpos(strtolower($company['address']), $searchLower) !== false;
                if (!$nameMatch && !$addressMatch) {
                    return false;
                }
            }

            return true;
        });

        // Sortowanie
        usort($filteredCompanies, function($a, $b) {
            // Najpierw według pozycji TOP (null na końcu)
            if (isset($a['topPosition']) && isset($b['topPosition'])) {
                return $a['topPosition'] <=> $b['topPosition'];
            } elseif (isset($a['topPosition'])) {
                return -1;
            } elseif (isset($b['topPosition'])) {
                return 1;
            }
            // Potem alfabetycznie
            return strcmp($a['name'] ?? '', $b['name'] ?? '');
        });

        // Dodaj nazwy kategorii
        foreach ($filteredCompanies as &$company) {
            if (isset($company['categoryId'])) {
                $category = array_filter($categories, function($cat) use ($company) {
                    return $cat['id'] == $company['categoryId'];
                });
                if (!empty($category)) {
                    $category = array_values($category)[0];
                    $company['category_name'] = $category['name'] ?? '';
                    $company['category_icon'] = $category['icon'] ?? '';
                }
            }
        }

        // Paginacja
        $total = count($filteredCompanies);
        $offset = ($page - 1) * $limit;
        $paginatedCompanies = array_slice($filteredCompanies, $offset, $limit);

        echo json_encode([
            'success' => true,
            'data' => [
                'companies' => array_values($paginatedCompanies),
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania firm',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz kategorie
 */
function getCategories() {
    global $categoriesFile;

    try {
        $categories = loadData($categoriesFile);

        // Filtruj kategorie główne (bez parentId)
        $mainCategories = array_filter($categories, function($category) {
            return !isset($category['parentId']) || $category['parentId'] === null;
        });

        // Sortuj według sortOrder i nazwy
        usort($mainCategories, function($a, $b) {
            $sortA = $a['sortOrder'] ?? 999;
            $sortB = $b['sortOrder'] ?? 999;
            if ($sortA === $sortB) {
                return strcmp($a['name'] ?? '', $b['name'] ?? '');
            }
            return $sortA <=> $sortB;
        });

        echo json_encode([
            'success' => true,
            'data' => array_values($mainCategories)
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Ustaw pozycję TOP dla firmy
 */
function setTopPosition() {
    global $companiesFile;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $companyId = $input['companyId'] ?? null;
        $topPosition = $input['topPosition'] ?? null;

        if (!$companyId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID firmy jest wymagane'
            ]);
            return;
        }

        $companies = loadData($companiesFile);

        // Jeśli ustawiamy pozycję TOP, usuń ją z innych firm
        if ($topPosition !== null && $topPosition >= 1 && $topPosition <= 3) {
            foreach ($companies as &$company) {
                if (isset($company['topPosition']) && $company['topPosition'] == $topPosition) {
                    $company['topPosition'] = null;
                }
            }
        }

        // Znajdź firmę i ustaw nową pozycję
        $companyFound = false;
        foreach ($companies as &$company) {
            if ($company['id'] == $companyId) {
                $company['topPosition'] = $topPosition;
                $companyFound = true;
                break;
            }
        }

        if (!$companyFound) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Firma nie została znaleziona'
            ]);
            return;
        }

        // Zapisz zmiany
        if (saveData($companiesFile, $companies)) {
            echo json_encode([
                'success' => true,
                'message' => 'Pozycja TOP została zaktualizowana'
            ]);
        } else {
            throw new Exception('Nie udało się zapisać danych');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd aktualizacji pozycji TOP',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Utwórz nową firmę
 */
function createCompany() {
    global $companiesFile;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $requiredFields = ['name', 'categoryId'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Pole {$field} jest wymagane"
                ]);
                return;
            }
        }

        $companies = loadData($companiesFile);
        $newId = getNextId($companies);

        // Generuj slug
        $slug = generateSlug($input['name']);

        // Utwórz nową firmę
        $newCompany = [
            'id' => $newId,
            'name' => $input['name'],
            'slug' => $slug,
            'description' => $input['description'] ?? null,
            'categoryId' => (int)$input['categoryId'],
            'subcategoryId' => isset($input['subcategoryId']) ? (int)$input['subcategoryId'] : null,
            'address' => $input['address'] ?? null,
            'postalCode' => $input['postalCode'] ?? null,
            'city' => $input['city'] ?? 'Żyrardów',
            'phone' => $input['phone'] ?? null,
            'email' => $input['email'] ?? null,
            'website' => $input['website'] ?? null,
            'logo' => $input['logo'] ?? null,
            'status' => $input['status'] ?? 'pending',
            'topPosition' => null,
            'publishedAt' => ($input['status'] ?? 'pending') === 'active' ? date('c') : null,
            'createdAt' => date('c'),
            'updatedAt' => date('c')
        ];

        // Dodaj do listy firm
        $companies[] = $newCompany;

        // Zapisz do pliku
        if (saveData($companiesFile, $companies)) {
            echo json_encode([
                'success' => true,
                'message' => 'Firma została utworzona pomyślnie',
                'data' => ['id' => $newId]
            ]);
        } else {
            throw new Exception('Nie udało się zapisać danych');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd tworzenia firmy',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Generuj slug z nazwy
 */
function generateSlug($name) {
    $slug = strtolower($name);
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    $slug = trim($slug, '-');
    return $slug;
}
?>
