<?php
/**
 * API dla zarządzania firmami z pozycjami TOP
 * Żyrardów Poleca - Panel Administracyjny
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Konfiguracja bazy danych
$config = [
    'host' => 'localhost',
    'dbname' => 'zyrardow_poleca_db',
    'username' => 'zyrardow_admin',
    'password' => 'ZyrardowPoleca2024!@#',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Błąd połączenia z bazą danych',
        'error' => $e->getMessage()
    ]);
    exit();
}

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'top') {
            getTopCompanies($pdo);
        } elseif ($path === 'all') {
            getAllCompanies($pdo);
        } elseif ($path === 'categories') {
            getCategories($pdo);
        } else {
            getCompanies($pdo);
        }
        break;
    
    case 'POST':
        if ($path === 'create') {
            createCompany($pdo);
        } elseif ($path === 'set-top-position') {
            setTopPosition($pdo);
        }
        break;
    
    case 'PUT':
        if ($path === 'update') {
            updateCompany($pdo);
        }
        break;
    
    case 'DELETE':
        if ($path === 'delete') {
            deleteCompany($pdo);
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz TOP 3 firmy
 */
function getTopCompanies($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT c.*, cat.name as category_name, cat.icon as category_icon
            FROM companies c
            LEFT JOIN categories cat ON c.categoryId = cat.id
            WHERE c.status = 'active' AND c.topPosition IS NOT NULL
            ORDER BY c.topPosition ASC
            LIMIT 3
        ");
        $stmt->execute();
        $companies = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $companies
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania TOP firm',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie firmy z paginacją i filtrami
 */
function getAllCompanies($pdo) {
    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';
        $categoryId = $_GET['categoryId'] ?? '';
        $search = $_GET['search'] ?? '';
        
        $offset = ($page - 1) * $limit;
        
        // Budowanie zapytania WHERE
        $whereConditions = [];
        $params = [];
        
        if ($status) {
            $whereConditions[] = "c.status = :status";
            $params['status'] = $status;
        }
        
        if ($categoryId) {
            $whereConditions[] = "c.categoryId = :categoryId";
            $params['categoryId'] = $categoryId;
        }
        
        if ($search) {
            $whereConditions[] = "(c.name LIKE :search OR c.address LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Zapytanie główne
        $stmt = $pdo->prepare("
            SELECT c.*, cat.name as category_name, cat.icon as category_icon
            FROM companies c
            LEFT JOIN categories cat ON c.categoryId = cat.id
            {$whereClause}
            ORDER BY c.topPosition ASC, c.name ASC
            LIMIT :limit OFFSET :offset
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue(":{$key}", $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        
        $stmt->execute();
        $companies = $stmt->fetchAll();
        
        // Zapytanie dla liczby wszystkich rekordów
        $countStmt = $pdo->prepare("
            SELECT COUNT(*) as total
            FROM companies c
            {$whereClause}
        ");
        
        foreach ($params as $key => $value) {
            $countStmt->bindValue(":{$key}", $value);
        }
        $countStmt->execute();
        $total = $countStmt->fetch()['total'];
        
        echo json_encode([
            'success' => true,
            'data' => [
                'companies' => $companies,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => (int)$total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania firm',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz kategorie
 */
function getCategories($pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT id, name, slug, icon, color, parentId
            FROM categories
            WHERE parentId IS NULL
            ORDER BY sortOrder ASC, name ASC
        ");
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'data' => $categories
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kategorii',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Ustaw pozycję TOP dla firmy
 */
function setTopPosition($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $companyId = $input['companyId'] ?? null;
        $topPosition = $input['topPosition'] ?? null;
        
        if (!$companyId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID firmy jest wymagane'
            ]);
            return;
        }
        
        $pdo->beginTransaction();
        
        // Jeśli ustawiamy pozycję TOP, sprawdź czy nie jest już zajęta
        if ($topPosition !== null && $topPosition >= 1 && $topPosition <= 3) {
            // Usuń pozycję TOP z innej firmy jeśli istnieje
            $stmt = $pdo->prepare("UPDATE companies SET topPosition = NULL WHERE topPosition = :topPosition");
            $stmt->execute(['topPosition' => $topPosition]);
        }
        
        // Ustaw nową pozycję TOP
        $stmt = $pdo->prepare("UPDATE companies SET topPosition = :topPosition WHERE id = :companyId");
        $stmt->execute([
            'topPosition' => $topPosition,
            'companyId' => $companyId
        ]);
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Pozycja TOP została zaktualizowana'
        ]);
    } catch (PDOException $e) {
        $pdo->rollBack();
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd aktualizacji pozycji TOP',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Utwórz nową firmę
 */
function createCompany($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $requiredFields = ['name', 'categoryId'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Pole {$field} jest wymagane"
                ]);
                return;
            }
        }
        
        // Generuj slug
        $slug = generateSlug($input['name']);
        
        $stmt = $pdo->prepare("
            INSERT INTO companies (name, slug, description, categoryId, subcategoryId, address, postalCode, city, phone, email, website, logo, status, publishedAt)
            VALUES (:name, :slug, :description, :categoryId, :subcategoryId, :address, :postalCode, :city, :phone, :email, :website, :logo, :status, NOW())
        ");
        
        $stmt->execute([
            'name' => $input['name'],
            'slug' => $slug,
            'description' => $input['description'] ?? null,
            'categoryId' => $input['categoryId'],
            'subcategoryId' => $input['subcategoryId'] ?? null,
            'address' => $input['address'] ?? null,
            'postalCode' => $input['postalCode'] ?? null,
            'city' => $input['city'] ?? 'Żyrardów',
            'phone' => $input['phone'] ?? null,
            'email' => $input['email'] ?? null,
            'website' => $input['website'] ?? null,
            'logo' => $input['logo'] ?? null,
            'status' => $input['status'] ?? 'pending'
        ]);
        
        $companyId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'Firma została utworzona pomyślnie',
            'data' => ['id' => $companyId]
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd tworzenia firmy',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Generuj slug z nazwy
 */
function generateSlug($name) {
    $slug = strtolower($name);
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    $slug = trim($slug, '-');
    return $slug;
}
?>
