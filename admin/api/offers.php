<?php
/**
 * API dla zarządzania ofertami specjalnymi
 * Żyrardów Poleca - Panel Administracyjny
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'active') {
            getActiveOffers();
        } else {
            getAllOffers();
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz aktywne oferty specjalne
 */
function getActiveOffers() {
    try {
        $limit = (int)($_GET['limit'] ?? 10);
        
        // Przykładowe dane ofert
        $offers = [
            [
                'id' => 1,
                'title' => 'Kolacja dla dwojga z winem',
                'description' => 'Romantyczna kolacja dla dwojga z butelką wina w cenie. Menu degustacyjne z przystawką, zupą, daniem głównym i deserem.',
                'price' => 199,
                'original_price' => 250,
                'validUntil' => '2024-12-31',
                'badge_type' => 'hot',
                'badge_text' => '🔥 HOT',
                'company_id' => 1,
                'company_name' => 'Restauracja Stary Młyn',
                'company_logo' => 'images/business-logo1.jpg',
                'category_name' => 'Gastronomia',
                'status' => 'active'
            ],
            [
                'id' => 2,
                'title' => 'Pakiet badań profilaktycznych',
                'description' => 'Kompleksowy pakiet badań obejmujący morfologię, biochemię, EKG i konsultację lekarską.',
                'price' => 249,
                'original_price' => 350,
                'validUntil' => '2024-11-30',
                'badge_type' => 'new',
                'badge_text' => '✨ NOWOŚĆ',
                'company_id' => 2,
                'company_name' => 'Centrum Medyczne Zdrowie',
                'company_logo' => 'images/business-logo2.jpg',
                'category_name' => 'Medycyna',
                'status' => 'active'
            ]
        ];
        
        // Ogranicz liczbę wyników
        $offers = array_slice($offers, 0, $limit);
        
        echo json_encode([
            'success' => true,
            'data' => $offers
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania ofert',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie oferty
 */
function getAllOffers() {
    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';
        
        // Przykładowe dane - w rzeczywistości pobierz z bazy danych
        $allOffers = [
            [
                'id' => 1,
                'title' => 'Kolacja dla dwojga z winem',
                'description' => 'Romantyczna kolacja dla dwojga z butelką wina w cenie. Menu degustacyjne z przystawką, zupą, daniem głównym i deserem.',
                'price' => 199,
                'original_price' => 250,
                'validUntil' => '2024-12-31',
                'badge_type' => 'hot',
                'badge_text' => '🔥 HOT',
                'company_id' => 1,
                'company_name' => 'Restauracja Stary Młyn',
                'company_logo' => 'images/business-logo1.jpg',
                'category_name' => 'Gastronomia',
                'status' => 'active',
                'createdAt' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'title' => 'Pakiet badań profilaktycznych',
                'description' => 'Kompleksowy pakiet badań obejmujący morfologię, biochemię, EKG i konsultację lekarską.',
                'price' => 249,
                'original_price' => 350,
                'validUntil' => '2024-11-30',
                'badge_type' => 'new',
                'badge_text' => '✨ NOWOŚĆ',
                'company_id' => 2,
                'company_name' => 'Centrum Medyczne Zdrowie',
                'company_logo' => 'images/business-logo2.jpg',
                'category_name' => 'Medycyna',
                'status' => 'active',
                'createdAt' => '2024-02-01 14:20:00'
            ]
        ];
        
        // Filtruj po statusie jeśli podano
        if ($status) {
            $allOffers = array_filter($allOffers, function($offer) use ($status) {
                return $offer['status'] === $status;
            });
        }
        
        $total = count($allOffers);
        $offset = ($page - 1) * $limit;
        $offers = array_slice($allOffers, $offset, $limit);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'offers' => $offers,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania ofert',
            'error' => $e->getMessage()
        ]);
    }
}
?>
