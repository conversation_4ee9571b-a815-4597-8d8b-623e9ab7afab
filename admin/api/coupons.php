<?php
/**
 * API dla zarządzania kuponami
 * Żyrardów Poleca - Panel Administracyjny
 * System plików JSON
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Ścieżki do plików danych
$dataDir = __DIR__ . '/../data/';
$couponsFile = $dataDir . 'coupons.json';

// Utwórz katalog danych jeśli nie istnieje
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}

// Funkcje pomocnicze
function loadData($file) {
    if (!file_exists($file)) {
        return [];
    }
    $content = file_get_contents($file);
    return $content ? json_decode($content, true) : [];
}

function saveData($file, $data) {
    return file_put_contents($file, json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
}

function getNextId($data) {
    if (empty($data)) return 1;
    $ids = array_column($data, 'id');
    return max($ids) + 1;
}

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'active') {
            getActiveCoupons();
        } else {
            getAllCoupons();
        }
        break;

    case 'POST':
        if ($path === 'create') {
            createCoupon();
        }
        break;

    case 'PUT':
        if ($path === 'update') {
            updateCoupon();
        }
        break;

    case 'DELETE':
        if ($path === 'delete') {
            deleteCoupon();
        }
        break;

    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz aktywne kupony
 */
function getActiveCoupons() {
    global $couponsFile;

    try {
        $limit = (int)($_GET['limit'] ?? 10);
        $coupons = loadData($couponsFile);

        // Filtruj aktywne kupony
        $activeCoupons = array_filter($coupons, function($coupon) {
            return isset($coupon['status']) && $coupon['status'] === 'active';
        });

        // Ogranicz liczbę wyników
        $activeCoupons = array_slice($activeCoupons, 0, $limit);

        echo json_encode([
            'success' => true,
            'data' => array_values($activeCoupons)
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kuponów',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie kupony
 */
function getAllCoupons() {
    global $couponsFile;

    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';

        $allCoupons = loadData($couponsFile);

        // Filtruj po statusie jeśli podano
        if ($status) {
            $allCoupons = array_filter($allCoupons, function($coupon) use ($status) {
                return isset($coupon['status']) && $coupon['status'] === $status;
            });
        }

        $total = count($allCoupons);
        $offset = ($page - 1) * $limit;
        $coupons = array_slice($allCoupons, $offset, $limit);

        echo json_encode([
            'success' => true,
            'data' => [
                'coupons' => array_values($coupons),
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kuponów',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Utwórz nowy kupon
 */
function createCoupon() {
    global $couponsFile;

    try {
        $input = json_decode(file_get_contents('php://input'), true);

        $requiredFields = ['title', 'code', 'discount_type', 'discount_value'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Pole {$field} jest wymagane"
                ]);
                return;
            }
        }

        $coupons = loadData($couponsFile);
        $newId = getNextId($coupons);

        // Utwórz nowy kupon
        $newCoupon = [
            'id' => $newId,
            'title' => $input['title'],
            'description' => $input['description'] ?? '',
            'code' => strtoupper($input['code']),
            'discount_type' => $input['discount_type'],
            'discount_value' => (float)$input['discount_value'],
            'validUntil' => $input['validUntil'] ?? null,
            'company_id' => isset($input['company_id']) ? (int)$input['company_id'] : null,
            'company_name' => $input['company_name'] ?? '',
            'company_logo' => $input['company_logo'] ?? '',
            'status' => $input['status'] ?? 'active',
            'createdAt' => date('c'),
            'updatedAt' => date('c')
        ];

        // Dodaj do listy kuponów
        $coupons[] = $newCoupon;

        // Zapisz do pliku
        if (saveData($couponsFile, $coupons)) {
            echo json_encode([
                'success' => true,
                'message' => 'Kupon został utworzony pomyślnie',
                'data' => ['id' => $newId]
            ]);
        } else {
            throw new Exception('Nie udało się zapisać danych');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd tworzenia kuponu',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Aktualizuj kupon
 */
function updateCoupon() {
    global $couponsFile;

    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $couponId = $input['id'] ?? null;

        if (!$couponId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID kuponu jest wymagane'
            ]);
            return;
        }

        $coupons = loadData($couponsFile);
        $couponFound = false;

        foreach ($coupons as &$coupon) {
            if ($coupon['id'] == $couponId) {
                // Aktualizuj dane kuponu
                $coupon['title'] = $input['title'] ?? $coupon['title'];
                $coupon['description'] = $input['description'] ?? $coupon['description'];
                $coupon['code'] = isset($input['code']) ? strtoupper($input['code']) : $coupon['code'];
                $coupon['discount_type'] = $input['discount_type'] ?? $coupon['discount_type'];
                $coupon['discount_value'] = isset($input['discount_value']) ? (float)$input['discount_value'] : $coupon['discount_value'];
                $coupon['validUntil'] = $input['validUntil'] ?? $coupon['validUntil'];
                $coupon['status'] = $input['status'] ?? $coupon['status'];
                $coupon['updatedAt'] = date('c');

                $couponFound = true;
                break;
            }
        }

        if (!$couponFound) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Kupon nie został znaleziony'
            ]);
            return;
        }

        if (saveData($couponsFile, $coupons)) {
            echo json_encode([
                'success' => true,
                'message' => 'Kupon został zaktualizowany pomyślnie'
            ]);
        } else {
            throw new Exception('Nie udało się zapisać danych');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd aktualizacji kuponu',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Usuń kupon
 */
function deleteCoupon() {
    global $couponsFile;

    try {
        $couponId = $_GET['id'] ?? null;

        if (!$couponId) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID kuponu jest wymagane'
            ]);
            return;
        }

        $coupons = loadData($couponsFile);
        $originalCount = count($coupons);

        // Usuń kupon z listy
        $coupons = array_filter($coupons, function($coupon) use ($couponId) {
            return $coupon['id'] != $couponId;
        });

        if (count($coupons) === $originalCount) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'message' => 'Kupon nie został znaleziony'
            ]);
            return;
        }

        // Resetuj indeksy tablicy
        $coupons = array_values($coupons);

        if (saveData($couponsFile, $coupons)) {
            echo json_encode([
                'success' => true,
                'message' => 'Kupon został usunięty pomyślnie'
            ]);
        } else {
            throw new Exception('Nie udało się zapisać danych');
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd usuwania kuponu',
            'error' => $e->getMessage()
        ]);
    }
}
?>
