<?php
/**
 * API dla zarządzania kuponami
 * Żyrardów Poleca - Panel Administracyjny
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Obsługa preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Routing
$method = $_SERVER['REQUEST_METHOD'];
$path = $_GET['path'] ?? '';

switch ($method) {
    case 'GET':
        if ($path === 'active') {
            getActiveCoupons();
        } else {
            getAllCoupons();
        }
        break;
    
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Metoda nie jest obsługiwana'
        ]);
        break;
}

/**
 * Pobierz aktywne kupony
 */
function getActiveCoupons() {
    try {
        $limit = (int)($_GET['limit'] ?? 10);
        
        // Przykładowe dane kuponów
        $coupons = [
            [
                'id' => 1,
                'title' => 'Rabat 20% na dania główne',
                'description' => 'Rabat obowiązuje od poniedziałku do czwartku w godzinach 12:00-16:00.',
                'code' => 'STARYMLYNRABAT',
                'discount_type' => 'percentage',
                'discount_value' => 20,
                'validUntil' => '2025-12-31',
                'company_id' => 1,
                'company_name' => 'Restauracja Stary Młyn',
                'company_logo' => 'images/business-logo1.jpg',
                'status' => 'active'
            ],
            [
                'id' => 2,
                'title' => 'Rabat 15% na badania laboratoryjne',
                'description' => 'Rabat obowiązuje na wszystkie badania laboratoryjne w pakiecie.',
                'code' => 'ZDROWIE15',
                'discount_type' => 'percentage',
                'discount_value' => 15,
                'validUntil' => '2024-11-30',
                'company_id' => 2,
                'company_name' => 'Centrum Medyczne Zdrowie',
                'company_logo' => 'images/business-logo2.jpg',
                'status' => 'active'
            ],
            [
                'id' => 3,
                'title' => 'Rabat 30% na koloryzację',
                'description' => 'Rabat obowiązuje na wszystkie usługi koloryzacji włosów.',
                'code' => 'BELLA30',
                'discount_type' => 'percentage',
                'discount_value' => 30,
                'validUntil' => '2025-12-15',
                'company_id' => 3,
                'company_name' => 'Salon Fryzjerski Bella',
                'company_logo' => 'images/business-logo3.jpg',
                'status' => 'active'
            ],
            [
                'id' => 4,
                'title' => 'Rabat 25% na desery',
                'description' => 'Rabat obowiązuje na wszystkie desery i ciasta w lokalu.',
                'code' => 'DESER25',
                'discount_type' => 'percentage',
                'discount_value' => 25,
                'validUntil' => '2025-11-15',
                'company_id' => 4,
                'company_name' => 'Kawiarnia Słodki Kącik',
                'company_logo' => 'images/business-logo1.jpg',
                'status' => 'active'
            ]
        ];
        
        // Ogranicz liczbę wyników
        $coupons = array_slice($coupons, 0, $limit);
        
        echo json_encode([
            'success' => true,
            'data' => $coupons
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kuponów',
            'error' => $e->getMessage()
        ]);
    }
}

/**
 * Pobierz wszystkie kupony
 */
function getAllCoupons() {
    try {
        $page = (int)($_GET['page'] ?? 1);
        $limit = (int)($_GET['limit'] ?? 20);
        $status = $_GET['status'] ?? '';
        
        // Przykładowe dane - w rzeczywistości pobierz z bazy danych
        $allCoupons = [
            [
                'id' => 1,
                'title' => 'Rabat 20% na dania główne',
                'description' => 'Rabat obowiązuje od poniedziałku do czwartku w godzinach 12:00-16:00.',
                'code' => 'STARYMLYNRABAT',
                'discount_type' => 'percentage',
                'discount_value' => 20,
                'validUntil' => '2025-12-31',
                'company_id' => 1,
                'company_name' => 'Restauracja Stary Młyn',
                'company_logo' => 'images/business-logo1.jpg',
                'status' => 'active',
                'createdAt' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'title' => 'Rabat 15% na badania laboratoryjne',
                'description' => 'Rabat obowiązuje na wszystkie badania laboratoryjne w pakiecie.',
                'code' => 'ZDROWIE15',
                'discount_type' => 'percentage',
                'discount_value' => 15,
                'validUntil' => '2024-11-30',
                'company_id' => 2,
                'company_name' => 'Centrum Medyczne Zdrowie',
                'company_logo' => 'images/business-logo2.jpg',
                'status' => 'active',
                'createdAt' => '2024-02-01 14:20:00'
            ]
        ];
        
        // Filtruj po statusie jeśli podano
        if ($status) {
            $allCoupons = array_filter($allCoupons, function($coupon) use ($status) {
                return $coupon['status'] === $status;
            });
        }
        
        $total = count($allCoupons);
        $offset = ($page - 1) * $limit;
        $coupons = array_slice($allCoupons, $offset, $limit);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'coupons' => $coupons,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Błąd pobierania kuponów',
            'error' => $e->getMessage()
        ]);
    }
}
?>
