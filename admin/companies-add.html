<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dodaj firmę - Panel Administracyjny - Żyrardów Poleca</title>
    <meta name="description" content="Panel administracyjny portalu Żyrardów Poleca - dodawanie nowej firmy">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/companies-add.css">
    <link rel="icon" href="../favicon.ico" type="image/x-icon">
    <!-- HugeRTE WYSIWYG Editor (darmowa wersja TinyMCE v6) -->
    <script src="https://cdn.jsdelivr.net/npm/@hugerte/hugerte@6/dist/hugerte.min.js"></script>
</head>
<body class="companies-add-page">
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="../images/logo.png" alt="Żyrardów Poleca Logo" class="sidebar-logo">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="sidebar-user">
                <div class="user-avatar">
                    <img src="images/admin-avatar.png" alt="Admin Avatar">
                </div>
                <div class="user-info">
                    <h3>Administrator</h3>
                    <p><EMAIL></p>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="companies.html">
                            <i class="fas fa-building"></i>
                            <span>Firmy</span>
                        </a>
                    </li>
                    <li>
                        <a href="offers.html">
                            <i class="fas fa-tag"></i>
                            <span>Oferty</span>
                        </a>
                    </li>
                    <li>
                        <a href="coupons.html">
                            <i class="fas fa-ticket-alt"></i>
                            <span>Kupony</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.html">
                            <i class="fas fa-list"></i>
                            <span>Kategorie</span>
                        </a>
                    </li>
                                        <li>
                        <a href="news.html">
                            <i class="fas fa-newspaper"></i>
                            <span>Wiadomości</span>
                        </a>
                    </li>
<li>
                        <a href="users.html">
                            <i class="fas fa-users"></i>
                            <span>Użytkownicy</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span>Ustawienia</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logoutBtn" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <div class="header-title">
                    <h1>Dodaj nową firmę</h1>
                    <p>Wprowadź dane firmy, która ma zostać dodana do katalogu</p>
                </div>
                <div class="header-actions">
                    <a href="companies.html" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Powrót do listy firm
                    </a>
                </div>
            </header>

            <div class="content-body">
                <form id="company-form" class="form-container">
                    <!-- Tabs Navigation -->
                    <div class="form-tabs">
                        <button type="button" class="tab-btn active" data-tab="basic-info">Podstawowe informacje</button>
                        <button type="button" class="tab-btn" data-tab="contact">Kontakt</button>
                        <button type="button" class="tab-btn" data-tab="details">Szczegóły</button>
                        <button type="button" class="tab-btn" data-tab="media">Media</button>
                        <button type="button" class="tab-btn" data-tab="seo">SEO</button>
                    </div>

                    <!-- Tab Content -->
                    <div class="form-content">
                        <!-- Basic Info Tab -->
                        <div class="tab-content active" id="basic-info">
                            <h2>Podstawowe informacje</h2>
                            <p class="form-description">Wprowadź podstawowe informacje o firmie</p>

                            <div class="form-section">
                                <div class="form-group">
                                    <label for="company-name">Nazwa firmy <span class="required">*</span></label>
                                    <input type="text" id="company-name" name="company-name" class="form-control" required>
                                </div>

                                <div class="form-group">
                                    <label for="company-category">Kategoria <span class="required">*</span></label>
                                    <select id="company-category" name="company-category" class="form-control" required>
                                        <option value="">Wybierz kategorię</option>
                                        <option value="1">Restauracje i kawiarnie</option>
                                        <option value="2">Sklepy</option>
                                        <option value="3">Usługi</option>
                                        <option value="4">Zdrowie i uroda</option>
                                        <option value="5">Rozrywka i rekreacja</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="company-subcategory">Podkategoria</label>
                                    <select id="company-subcategory" name="company-subcategory" class="form-control">
                                        <option value="">Wybierz podkategorię</option>
                                        <!-- Podkategorie będą dynamicznie ładowane w zależności od wybranej kategorii -->
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="company-description">Opis firmy <span class="required">*</span></label>
                                    <textarea id="company-description" name="company-description" class="form-control" rows="5" required></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="company-short-description">Krótki opis (do 150 znaków)</label>
                                    <textarea id="company-short-description" name="company-short-description" class="form-control" rows="2" maxlength="150"></textarea>
                                    <div class="char-counter"><span id="short-desc-counter">0</span>/150</div>
                                </div>

                                <div class="form-group">
                                    <label for="company-tags">Tagi (oddzielone przecinkami)</label>
                                    <input type="text" id="company-tags" name="company-tags" class="form-control" placeholder="np. restauracja, pizza, włoska kuchnia">
                                </div>

                                <div class="form-group">
                                    <label>Status</label>
                                    <div class="radio-group">
                                        <div class="radio-option">
                                            <input type="radio" id="status-active" name="company-status" value="active" checked>
                                            <label for="status-active">Aktywna</label>
                                        </div>
                                        <div class="radio-option">
                                            <input type="radio" id="status-pending" name="company-status" value="pending">
                                            <label for="status-pending">Oczekująca</label>
                                        </div>
                                        <div class="radio-option">
                                            <input type="radio" id="status-inactive" name="company-status" value="inactive">
                                            <label for="status-inactive">Nieaktywna</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-primary next-tab" data-next="contact">Dalej: Kontakt</button>
                            </div>
                        </div>

                        <!-- Contact Tab -->
                        <div class="tab-content" id="contact">
                            <h2>Dane kontaktowe</h2>
                            <p class="form-description">Wprowadź dane kontaktowe firmy</p>

                            <div class="form-section">
                                <div class="form-group">
                                    <label for="company-address">Adres <span class="required">*</span></label>
                                    <input type="text" id="company-address" name="company-address" class="form-control" required>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="company-postal-code">Kod pocztowy <span class="required">*</span></label>
                                        <input type="text" id="company-postal-code" name="company-postal-code" class="form-control" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="company-city">Miasto <span class="required">*</span></label>
                                        <input type="text" id="company-city" name="company-city" class="form-control" value="Żyrardów" required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="company-phone">Telefon <span class="required">*</span></label>
                                    <input type="tel" id="company-phone" name="company-phone" class="form-control" required>
                                </div>

                                <div class="form-group">
                                    <label for="company-email">Email</label>
                                    <input type="email" id="company-email" name="company-email" class="form-control">
                                </div>

                                <div class="form-group">
                                    <label for="company-website">Strona internetowa</label>
                                    <input type="url" id="company-website" name="company-website" class="form-control" placeholder="https://">
                                </div>

                                <div class="form-group">
                                    <label for="company-facebook">Facebook</label>
                                    <input type="url" id="company-facebook" name="company-facebook" class="form-control" placeholder="https://facebook.com/">
                                </div>

                                <div class="form-group">
                                    <label for="company-instagram">Instagram</label>
                                    <input type="url" id="company-instagram" name="company-instagram" class="form-control" placeholder="https://instagram.com/">
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-outline prev-tab" data-prev="basic-info">Wstecz: Podstawowe informacje</button>
                                <button type="button" class="btn btn-primary next-tab" data-next="details">Dalej: Szczegóły</button>
                            </div>
                        </div>

                        <!-- Details Tab -->
                        <div class="tab-content" id="details">
                            <h2>Szczegóły firmy</h2>
                            <p class="form-description">Wprowadź dodatkowe informacje o firmie</p>

                            <div class="form-section">
                                <div class="form-group">
                                    <label>Godziny otwarcia</label>
                                    <div class="opening-hours">
                                        <div class="day-row">
                                            <span class="day-name">Poniedziałek</span>
                                            <div class="hours-inputs">
                                                <input type="time" name="monday-open" class="form-control time-input">
                                                <span>-</span>
                                                <input type="time" name="monday-close" class="form-control time-input">
                                            </div>
                                            <div class="closed-checkbox">
                                                <input type="checkbox" id="monday-closed" name="monday-closed">
                                                <label for="monday-closed">Zamknięte</label>
                                            </div>
                                        </div>
                                        <div class="day-row">
                                            <span class="day-name">Wtorek</span>
                                            <div class="hours-inputs">
                                                <input type="time" name="tuesday-open" class="form-control time-input">
                                                <span>-</span>
                                                <input type="time" name="tuesday-close" class="form-control time-input">
                                            </div>
                                            <div class="closed-checkbox">
                                                <input type="checkbox" id="tuesday-closed" name="tuesday-closed">
                                                <label for="tuesday-closed">Zamknięte</label>
                                            </div>
                                        </div>
                                        <div class="day-row">
                                            <span class="day-name">Środa</span>
                                            <div class="hours-inputs">
                                                <input type="time" name="wednesday-open" class="form-control time-input">
                                                <span>-</span>
                                                <input type="time" name="wednesday-close" class="form-control time-input">
                                            </div>
                                            <div class="closed-checkbox">
                                                <input type="checkbox" id="wednesday-closed" name="wednesday-closed">
                                                <label for="wednesday-closed">Zamknięte</label>
                                            </div>
                                        </div>
                                        <div class="day-row">
                                            <span class="day-name">Czwartek</span>
                                            <div class="hours-inputs">
                                                <input type="time" name="thursday-open" class="form-control time-input">
                                                <span>-</span>
                                                <input type="time" name="thursday-close" class="form-control time-input">
                                            </div>
                                            <div class="closed-checkbox">
                                                <input type="checkbox" id="thursday-closed" name="thursday-closed">
                                                <label for="thursday-closed">Zamknięte</label>
                                            </div>
                                        </div>
                                        <div class="day-row">
                                            <span class="day-name">Piątek</span>
                                            <div class="hours-inputs">
                                                <input type="time" name="friday-open" class="form-control time-input">
                                                <span>-</span>
                                                <input type="time" name="friday-close" class="form-control time-input">
                                            </div>
                                            <div class="closed-checkbox">
                                                <input type="checkbox" id="friday-closed" name="friday-closed">
                                                <label for="friday-closed">Zamknięte</label>
                                            </div>
                                        </div>
                                        <div class="day-row">
                                            <span class="day-name">Sobota</span>
                                            <div class="hours-inputs">
                                                <input type="time" name="saturday-open" class="form-control time-input">
                                                <span>-</span>
                                                <input type="time" name="saturday-close" class="form-control time-input">
                                            </div>
                                            <div class="closed-checkbox">
                                                <input type="checkbox" id="saturday-closed" name="saturday-closed">
                                                <label for="saturday-closed">Zamknięte</label>
                                            </div>
                                        </div>
                                        <div class="day-row">
                                            <span class="day-name">Niedziela</span>
                                            <div class="hours-inputs">
                                                <input type="time" name="sunday-open" class="form-control time-input">
                                                <span>-</span>
                                                <input type="time" name="sunday-close" class="form-control time-input">
                                            </div>
                                            <div class="closed-checkbox">
                                                <input type="checkbox" id="sunday-closed" name="sunday-closed">
                                                <label for="sunday-closed">Zamknięte</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="company-payment-methods">Metody płatności</label>
                                    <div class="checkbox-group">
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="payment-cash" name="payment-methods[]" value="cash" checked>
                                            <label for="payment-cash">Gotówka</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="payment-card" name="payment-methods[]" value="card" checked>
                                            <label for="payment-card">Karta płatnicza</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="payment-online" name="payment-methods[]" value="online">
                                            <label for="payment-online">Płatności online</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="payment-blik" name="payment-methods[]" value="blik">
                                            <label for="payment-blik">BLIK</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="company-features">Udogodnienia</label>
                                    <div class="checkbox-group">
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="feature-wifi" name="features[]" value="wifi">
                                            <label for="feature-wifi">Wi-Fi</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="feature-parking" name="features[]" value="parking">
                                            <label for="feature-parking">Parking</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="feature-delivery" name="features[]" value="delivery">
                                            <label for="feature-delivery">Dostawa</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="feature-takeaway" name="features[]" value="takeaway">
                                            <label for="feature-takeaway">Na wynos</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="feature-reservation" name="features[]" value="reservation">
                                            <label for="feature-reservation">Rezerwacja</label>
                                        </div>
                                        <div class="checkbox-option">
                                            <input type="checkbox" id="feature-accessibility" name="features[]" value="accessibility">
                                            <label for="feature-accessibility">Dostępność dla niepełnosprawnych</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-outline prev-tab" data-prev="contact">Wstecz: Kontakt</button>
                                <button type="button" class="btn btn-primary next-tab" data-next="media">Dalej: Media</button>
                            </div>
                        </div>

                        <!-- Media Tab -->
                        <div class="tab-content" id="media">
                            <h2>Media</h2>
                            <p class="form-description">Dodaj logo i zdjęcia firmy</p>

                            <div class="form-section">
                                <div class="form-group">
                                    <label for="company-logo">Logo firmy <span class="required">*</span></label>
                                    <div class="file-upload">
                                        <div class="file-upload-preview" id="logo-preview">
                                            <div class="upload-placeholder">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                                <span>Przeciągnij i upuść lub kliknij, aby wybrać plik</span>
                                            </div>
                                        </div>
                                        <input type="file" id="company-logo" name="company-logo" accept="image/*" required>
                                    </div>
                                    <small class="form-text">Zalecany rozmiar: 400x400px, format: JPG, PNG</small>
                                </div>

                                <div class="form-group">
                                    <label for="company-photos">Zdjęcia firmy</label>
                                    <div class="file-upload multiple">
                                        <div class="file-upload-preview" id="photos-preview">
                                            <div class="upload-placeholder">
                                                <i class="fas fa-images"></i>
                                                <span>Przeciągnij i upuść lub kliknij, aby wybrać pliki</span>
                                            </div>
                                        </div>
                                        <input type="file" id="company-photos" name="company-photos[]" accept="image/*" multiple>
                                    </div>
                                    <small class="form-text">Możesz dodać do 10 zdjęć. Zalecany rozmiar: 1200x800px, format: JPG, PNG</small>
                                </div>

                                <div class="form-group">
                                    <label for="company-video">Link do filmu (YouTube, Vimeo)</label>
                                    <input type="url" id="company-video" name="company-video" class="form-control" placeholder="https://www.youtube.com/watch?v=...">
                                </div>

                                <div class="form-group">
                                    <label for="company-virtual-tour">Link do wirtualnego spaceru</label>
                                    <input type="url" id="company-virtual-tour" name="company-virtual-tour" class="form-control" placeholder="https://...">
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-outline prev-tab" data-prev="details">Wstecz: Szczegóły</button>
                                <button type="button" class="btn btn-primary next-tab" data-next="seo">Dalej: SEO</button>
                            </div>
                        </div>

                        <!-- SEO Tab -->
                        <div class="tab-content" id="seo">
                            <h2>SEO</h2>
                            <p class="form-description">Optymalizacja dla wyszukiwarek</p>

                            <div class="form-section">
                                <div class="form-group">
                                    <label for="company-meta-title">Meta tytuł</label>
                                    <input type="text" id="company-meta-title" name="company-meta-title" class="form-control">
                                    <small class="form-text">Zalecana długość: 50-60 znaków</small>
                                </div>

                                <div class="form-group">
                                    <label for="company-meta-description">Meta opis</label>
                                    <textarea id="company-meta-description" name="company-meta-description" class="form-control" rows="3"></textarea>
                                    <small class="form-text">Zalecana długość: 150-160 znaków</small>
                                </div>

                                <div class="form-group">
                                    <label for="company-meta-keywords">Meta słowa kluczowe</label>
                                    <input type="text" id="company-meta-keywords" name="company-meta-keywords" class="form-control" placeholder="słowo1, słowo2, słowo3">
                                    <small class="form-text">Oddziel słowa kluczowe przecinkami</small>
                                </div>

                                <div class="form-group">
                                    <label for="company-slug">Przyjazny URL</label>
                                    <div class="input-group">
                                        <span class="input-group-text">https://zyrardow.poleca.to/firma/</span>
                                        <input type="text" id="company-slug" name="company-slug" class="form-control">
                                    </div>
                                    <small class="form-text">Pozostaw puste, aby wygenerować automatycznie z nazwy firmy</small>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-outline prev-tab" data-prev="media">Wstecz: Media</button>
                                <button type="submit" class="btn btn-success">Zapisz firmę</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script src="js/admin.js"></script>
    <script src="js/companies-add.js"></script>
</body>
</html>
