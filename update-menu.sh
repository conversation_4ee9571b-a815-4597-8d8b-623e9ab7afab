#!/bin/bash

# Skrypt do aktualizacji menu na wszystkich stronach HTML

# Pobierz zawartość szablonu menu
MENU_TEMPLATE=$(cat menu-template.html)

# Lista wszystkich plików HTML (z wyjątkiem plików w katalogu admin)
HTML_FILES=$(find . -name "*.html" | grep -v "admin/")

# Funkcja do aktualizacji menu w pliku HTML
update_menu() {
    local file=$1
    local page_name=$(basename "$file" .html)
    
    # Tymczasowy plik
    local temp_file="${file}.tmp"
    
    # Skopiuj zawartość szablonu menu
    local menu_content="$MENU_TEMPLATE"
    
    # Ustaw aktywną stronę w menu
    if [[ "$page_name" == "index" ]]; then
        menu_content=${menu_content//<a href="index.html">/<a href="index.html" class="active">}
    elif [[ "$page_name" == "o-miescie" ]]; then
        menu_content=${menu_content//<a href="o-miescie.html">/<a href="o-miescie.html" class="active">}
    elif [[ "$page_name" == "historia" ]]; then
        menu_content=${menu_content//<a href="historia.html">/<a href="historia.html" class="active">}
    elif [[ "$page_name" == "zabytki" ]]; then
        menu_content=${menu_content//<a href="zabytki.html">/<a href="zabytki.html" class="active">}
    elif [[ "$page_name" == "powiat" ]]; then
        menu_content=${menu_content//<a href="powiat.html">/<a href="powiat.html" class="active">}
    elif [[ "$page_name" == "atrakcje" ]]; then
        menu_content=${menu_content//<a href="atrakcje.html">/<a href="atrakcje.html" class="active">}
    elif [[ "$page_name" == "osada-fabryczna" ]]; then
        menu_content=${menu_content//<a href="osada-fabryczna.html">/<a href="osada-fabryczna.html" class="active">}
    elif [[ "$page_name" == "park-dittricha" ]]; then
        menu_content=${menu_content//<a href="park-dittricha.html">/<a href="park-dittricha.html" class="active">}
    elif [[ "$page_name" == "muzeum-lniarstwa" ]]; then
        menu_content=${menu_content//<a href="muzeum-lniarstwa.html">/<a href="muzeum-lniarstwa.html" class="active">}
    elif [[ "$page_name" == "kosciol-mb-pocieszenia" ]]; then
        menu_content=${menu_content//<a href="kosciol-mb-pocieszenia.html">/<a href="kosciol-mb-pocieszenia.html" class="active">}
    elif [[ "$page_name" == "centrum-kultury" ]]; then
        menu_content=${menu_content//<a href="centrum-kultury.html">/<a href="centrum-kultury.html" class="active">}
    elif [[ "$page_name" == "aquapark" ]]; then
        menu_content=${menu_content//<a href="aquapark.html">/<a href="aquapark.html" class="active">}
    elif [[ "$page_name" == "kultura-sport" ]]; then
        menu_content=${menu_content//<a href="kultura-sport.html">/<a href="kultura-sport.html" class="active">}
    elif [[ "$page_name" == "turystyka" ]]; then
        menu_content=${menu_content//<a href="turystyka.html">/<a href="turystyka.html" class="active">}
    elif [[ "$page_name" == "noclegi" ]]; then
        menu_content=${menu_content//<a href="noclegi.html">/<a href="noclegi.html" class="active">}
    elif [[ "$page_name" == "gastronomia" ]]; then
        menu_content=${menu_content//<a href="gastronomia.html">/<a href="gastronomia.html" class="active">}
    elif [[ "$page_name" == "kupony" ]]; then
        menu_content=${menu_content//<a href="kupony.html">/<a href="kupony.html" class="active">}
    elif [[ "$page_name" == "oferty" ]]; then
        menu_content=${menu_content//<a href="oferty.html">/<a href="oferty.html" class="active">}
    elif [[ "$page_name" == "wydarzenia" ]]; then
        menu_content=${menu_content//<a href="wydarzenia.html">/<a href="wydarzenia.html" class="active">}
    elif [[ "$page_name" == "dla-biznesu" ]]; then
        menu_content=${menu_content//<a href="dla-biznesu.html">/<a href="dla-biznesu.html" class="active">}
    elif [[ "$page_name" == "kontakt" ]]; then
        menu_content=${menu_content//<a href="kontakt.html">/<a href="kontakt.html" class="active">}
    fi
    
    # Zastąp menu w pliku HTML
    awk -v menu="$menu_content" '
    BEGIN { in_menu = 0; menu_found = 0; }
    /<nav class="main-nav">/ { in_menu = 1; menu_found = 1; print menu; next; }
    /<\/div>/ && in_menu { in_menu = 0; next; }
    !in_menu { print; }
    END { if (!menu_found) print "Menu not found in " FILENAME > "/dev/stderr"; }
    ' "$file" > "$temp_file"
    
    # Sprawdź, czy menu zostało znalezione
    if grep -q "Menu not found" "$temp_file"; then
        echo "Menu not found in $file"
        rm "$temp_file"
        return 1
    fi
    
    # Zastąp oryginalny plik
    mv "$temp_file" "$file"
    echo "Updated menu in $file"
    return 0
}

# Aktualizuj menu w każdym pliku HTML
for file in $HTML_FILES; do
    echo "Processing $file..."
    update_menu "$file"
done

echo "Menu update completed!"
