<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Przykład szablonu - Żyrardów Poleca</title>
    <meta name="description" content="Przykład użycia systemu szablonów na portalu Żyrardów Poleca">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/menu.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Kontener na nagłówek - zostanie wypełniony przez templates.js -->
    <div id="header"></div>

    <!-- Kontener na nagłówek - zostanie wypełniony przez templates.js -->
    <div id="header"></div>

    <main>
        <section class="page-header">
            <div class="container">
                <h1>Przykład użycia szablonów</h1>
                <p class="lead">Ta strona demonstruje działanie systemu szablonów</p>
            </div>
        </section>

        <section class="content-section">
            <div class="container">
                <div class="content-box">
                    <h2>Jak działa system szablonów?</h2>
                    <p>System szablonów pozwala na centralne zarządzanie elementami, które powtarzają się na wielu stronach, takimi jak menu nawigacyjne czy stopka. Dzięki temu zmiana w jednym pliku szablonu automatycznie wpływa na wszystkie strony, które z niego korzystają.</p>
                    
                    <h3>Zalety systemu szablonów:</h3>
                    <ul>
                        <li>Łatwiejsze zarządzanie zawartością</li>
                        <li>Spójność interfejsu użytkownika</li>
                        <li>Szybsze wprowadzanie zmian</li>
                        <li>Mniejsza ilość kodu do utrzymania</li>
                    </ul>
                    
                    <h3>Jak zaimplementować system szablonów?</h3>
                    <p>Aby zaimplementować system szablonów na swojej stronie:</p>
                    <ol>
                        <li>Dodaj elementy kontenera (np. <code>&lt;div id="header"&gt;&lt;/div&gt;</code>) w miejscach, gdzie chcesz wstawić szablony</li>
                        <li>Dodaj odniesienie do pliku <code>templates.js</code> w sekcji skryptów</li>
                        <li>Stwórz pliki szablonów w folderze <code>templates/</code></li>
                    </ol>
                    
                    <div class="code-example">
                        <h4>Przykładowy kod HTML:</h4>
                        <pre><code>&lt;!-- Kontener na nagłówek --&gt;
&lt;div id="header"&gt;&lt;/div&gt;

&lt;!-- Treść strony --&gt;
&lt;main&gt;
    &lt;!-- Zawartość strony --&gt;
&lt;/main&gt;

&lt;!-- Kontener na stopkę --&gt;
&lt;div id="footer"&gt;&lt;/div&gt;

&lt;!-- Skrypty --&gt;
&lt;script src="js/templates.js"&gt;&lt;/script&gt;</code></pre>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Kontener na stopkę - zostanie wypełniony przez templates.js -->
    <div id="footer"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/menu.js"></script>
    <script src="js/templates.js"></script>
    <!-- Kontener na stopkę - zostanie wypełniony przez templates.js -->
    <div id="footer"></div>

</body>
</html>
