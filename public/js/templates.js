/**
 * System szablonów dla menu i stopki
 * Ładuje szablony z plików HTML i wstawia je do odpowiednich elementów na stronie
 */
document.addEventListener('DOMContentLoaded', function() {
    // Ładowanie szablonu nagłówka
    loadTemplate('header', 'templates/header.html', function() {
        // Sprawdź czy są zapisane menu w localStorage i zastosuj je
        applyCustomMenus();

        // Po załadowaniu nagłówka, oznacz aktywną stronę w menu
        markActivePage();

        // Inicjalizacja funkcjonalności menu po załadowaniu szablonu
        initMenuFunctionality();
    });

    // Ładowanie szablonu stopki
    loadTemplate('footer', 'templates/footer.html', function() {
        // Inicjalizacja funkcjonalności stopki po załadowaniu szablonu
        initFooterFunctionality();
    });

    /**
     * Funkcja ładująca szablon z pliku HTML i wstawiająca go do elementu o podanym ID
     * @param {string} elementId - ID elementu, do którego zostanie wstawiony szablon
     * @param {string} templatePath - Ścieżka do pliku szablonu
     * @param {Function} callback - Funkcja wywoływana po załadowaniu szablonu
     */
    function loadTemplate(elementId, templatePath, callback) {
        const element = document.getElementById(elementId);
        if (!element) return;

        fetch(templatePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Nie udało się załadować szablonu: ${response.status}`);
                }
                return response.text();
            })
            .then(html => {
                element.innerHTML = html;
                if (typeof callback === 'function') {
                    callback();
                }
            })
            .catch(error => {
                console.error('Błąd ładowania szablonu:', error);
            });
    }

    /**
     * Funkcja oznaczająca aktywną stronę w menu
     */
    function markActivePage() {
        // Pobierz aktualną ścieżkę strony
        const currentPath = window.location.pathname;
        const pageName = currentPath.split('/').pop() || 'index.html';

        // Usuń klasę 'active' ze wszystkich linków
        const allLinks = document.querySelectorAll('.nav-links a');
        allLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Dodaj klasę 'active' do odpowiedniego linku
        const activeLinks = document.querySelectorAll(`.nav-links a[href="${pageName}"]`);
        activeLinks.forEach(link => {
            link.classList.add('active');

            // Jeśli link jest w submenu, oznacz również nadrzędny link jako aktywny
            const parentLi = link.closest('.submenu')?.parentElement;
            if (parentLi) {
                const parentLink = parentLi.querySelector('a');
                if (parentLink) {
                    parentLink.classList.add('active');
                }
            }
        });
    }

    /**
     * Inicjalizacja funkcjonalności menu
     */
    function initMenuFunctionality() {
        // Obsługa przycisku wyszukiwania
        const searchToggle = document.querySelector('.search-toggle');
        const searchContainer = document.querySelector('.search-container');

        if (searchToggle && searchContainer) {
            searchToggle.addEventListener('click', function() {
                searchContainer.classList.toggle('active');
                if (searchContainer.classList.contains('active')) {
                    searchContainer.querySelector('input').focus();
                }
            });
        }

        // Obsługa menu mobilnego
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');

        if (mobileMenuBtn && navLinks) {
            mobileMenuBtn.addEventListener('click', function() {
                mobileMenuBtn.classList.toggle('active');
                navLinks.classList.toggle('active');
                document.body.classList.toggle('menu-open');
            });
        }

        // Obsługa submenu na urządzeniach mobilnych
        const hasSubmenuLinks = document.querySelectorAll('.has-submenu > a');

        hasSubmenuLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                if (window.innerWidth <= 992) {
                    e.preventDefault();
                    const parentLi = this.parentElement;
                    parentLi.classList.toggle('submenu-open');
                }
            });
        });
    }

    /**
     * Inicjalizacja funkcjonalności stopki
     */
    function initFooterFunctionality() {
        // Obsługa przycisku akceptacji cookies
        const cookieNotice = document.getElementById('cookieNotice');
        const acceptCookiesBtn = document.getElementById('acceptCookies');

        if (cookieNotice && acceptCookiesBtn) {
            // Sprawdź, czy użytkownik już zaakceptował cookies
            if (localStorage.getItem('cookiesAccepted') !== 'true') {
                cookieNotice.classList.add('active');
            }

            acceptCookiesBtn.addEventListener('click', function() {
                localStorage.setItem('cookiesAccepted', 'true');
                cookieNotice.classList.remove('active');
            });
        }

        // Obsługa przycisku "Powrót do góry"
        const backToTopBtn = document.querySelector('.back-to-top');

        if (backToTopBtn) {
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('active');
                } else {
                    backToTopBtn.classList.remove('active');
                }
            });

            backToTopBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }

    /**
     * Funkcja stosująca niestandardowe menu z localStorage
     */
    function applyCustomMenus() {
        // Sprawdź czy są zapisane menu w localStorage
        const savedHeaderMenu = localStorage.getItem('zyrardow_header_menu');
        const savedFooterMenu = localStorage.getItem('zyrardow_footer_menu');

        if (savedHeaderMenu) {
            try {
                const headerMenuData = JSON.parse(savedHeaderMenu);
                updateHeaderMenu(headerMenuData);
                console.log('Zastosowano niestandardowe menu nagłówka z localStorage');
            } catch (error) {
                console.error('Błąd podczas parsowania menu nagłówka:', error);
            }
        }

        if (savedFooterMenu) {
            try {
                const footerMenuData = JSON.parse(savedFooterMenu);
                updateFooterMenu(footerMenuData);
                console.log('Zastosowano niestandardowe menu stopki z localStorage');
            } catch (error) {
                console.error('Błąd podczas parsowania menu stopki:', error);
            }
        }
    }

    /**
     * Aktualizacja menu nagłówka
     * @param {Array} menuData - Dane menu nagłówka
     */
    function updateHeaderMenu(menuData) {
        const navLinks = document.querySelector('.nav-links');
        if (!navLinks) return;

        // Wyczyść istniejące menu
        navLinks.innerHTML = '';

        // Dodaj nowe pozycje menu
        menuData.forEach(item => {
            const li = document.createElement('li');
            if (item.children && item.children.length > 0) {
                li.className = 'has-submenu';
            }

            const a = document.createElement('a');
            a.href = item.url;
            a.textContent = item.text;
            if (item.target === '_blank') {
                a.target = '_blank';
                a.rel = 'noopener noreferrer';
            }
            li.appendChild(a);

            // Dodaj podmenu, jeśli istnieje
            if (item.children && item.children.length > 0) {
                const submenu = document.createElement('ul');
                submenu.className = 'submenu';

                item.children.forEach(subItem => {
                    const subLi = document.createElement('li');
                    const subA = document.createElement('a');
                    subA.href = subItem.url;
                    subA.textContent = subItem.text;
                    if (subItem.target === '_blank') {
                        subA.target = '_blank';
                        subA.rel = 'noopener noreferrer';
                    }
                    subLi.appendChild(subA);
                    submenu.appendChild(subLi);
                });

                li.appendChild(submenu);
            }

            navLinks.appendChild(li);
        });
    }

    /**
     * Aktualizacja menu stopki
     * @param {Array} menuData - Dane menu stopki
     */
    function updateFooterMenu(menuData) {
        const footerNav = document.querySelector('.footer-nav');
        const footerLinks = document.querySelector('.footer-links');

        if (!footerNav || !footerLinks) return;

        // Wyczyść istniejące menu
        footerNav.innerHTML = '';
        footerLinks.innerHTML = '';

        // Dodaj nowe pozycje menu
        menuData.forEach((item, index) => {
            // Ostatnia pozycja to linki w dolnej części stopki
            if (index === menuData.length - 1 && item.text === 'Informacje') {
                item.children.forEach(subItem => {
                    const li = document.createElement('li');
                    const a = document.createElement('a');
                    a.href = subItem.url;
                    a.textContent = subItem.text;
                    if (subItem.target === '_blank') {
                        a.target = '_blank';
                        a.rel = 'noopener noreferrer';
                    }
                    li.appendChild(a);
                    footerLinks.appendChild(li);
                });
            } else {
                // Kolumny menu w górnej części stopki
                const column = document.createElement('div');
                column.className = 'footer-nav-column';

                const title = document.createElement('h3');
                title.textContent = item.text;
                column.appendChild(title);

                const ul = document.createElement('ul');

                item.children.forEach(subItem => {
                    const li = document.createElement('li');
                    const a = document.createElement('a');
                    a.href = subItem.url;
                    a.textContent = subItem.text;
                    if (subItem.target === '_blank') {
                        a.target = '_blank';
                        a.rel = 'noopener noreferrer';
                    }
                    li.appendChild(a);
                    ul.appendChild(li);
                });

                column.appendChild(ul);
                footerNav.appendChild(column);
            }
        });
    }
});
