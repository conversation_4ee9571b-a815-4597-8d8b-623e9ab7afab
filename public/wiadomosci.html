<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wiadomości lokalne - Żyrardów Poleca</title>
    <meta name="description" content="Najnowsze wiadomości z Żyrardowa - wydarzenia lokalne, inwestycje, kultura, sport i biznes w naszym mieście">
    <meta name="keywords" content="wiadomości Żyrardów, wydarzenia lokalne, aktualności Żyrardów, news">

    <!-- Open Graph -->
    <meta property="og:title" content="Wiadomości lokalne - Żyrardów Poleca">
    <meta property="og:description" content="Najnowsze wiadomości z Żyrardowa - wydarzenia lokalne, inwestycje, kultura, sport i biznes">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://zyrardow.poleca.to/wiadomosci.html">
    <meta property="og:image" content="https://zyrardow.poleca.to/images/og-news.jpg">

    <!-- Favicon -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="images/apple-touch-icon.png">

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/menu.css">
    <link rel="stylesheet" href="css/wiadomosci.css">

    <!-- PWA -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#ff6600">
</head>
<body>
    <!-- Kontener na nagłówek - zostanie wypełniony przez templates.js -->
    <div id="header"></div>

    <!-- Hero Section -->
    <section class="news-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Wiadomości lokalne</h1>
                <p>Bądź na bieżąco z najnowszymi wydarzeniami w Żyrardowie</p>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="news-main">
        <div class="container">
            <!-- Filtry kategorii -->
            <section class="news-filters">
                <div class="filters-header">
                    <h2>Kategorie wiadomości</h2>
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="newsSearch" placeholder="Szukaj wiadomości...">
                    </div>
                </div>
                <div class="category-filters">
                    <button class="category-btn active" data-category="all">
                        <i class="fas fa-th-large"></i>
                        Wszystkie
                        <span class="count" id="countAll">0</span>
                    </button>
                    <button class="category-btn" data-category="local">
                        <i class="fas fa-map-marker-alt"></i>
                        Lokalne
                        <span class="count" id="countLocal">0</span>
                    </button>
                    <button class="category-btn" data-category="events">
                        <i class="fas fa-calendar-alt"></i>
                        Wydarzenia
                        <span class="count" id="countEvents">0</span>
                    </button>
                    <button class="category-btn" data-category="business">
                        <i class="fas fa-briefcase"></i>
                        Biznes
                        <span class="count" id="countBusiness">0</span>
                    </button>
                    <button class="category-btn" data-category="culture">
                        <i class="fas fa-theater-masks"></i>
                        Kultura
                        <span class="count" id="countCulture">0</span>
                    </button>
                    <button class="category-btn" data-category="sport">
                        <i class="fas fa-futbol"></i>
                        Sport
                        <span class="count" id="countSport">0</span>
                    </button>
                </div>
            </section>

            <!-- Lista wiadomości -->
            <section class="news-content">
                <div class="content-header">
                    <div class="results-info">
                        <span id="resultsCount">Ładowanie wiadomości...</span>
                    </div>
                    <div class="sort-options">
                        <label for="sortSelect">Sortuj:</label>
                        <select id="sortSelect">
                            <option value="newest">Najnowsze</option>
                            <option value="oldest">Najstarsze</option>
                            <option value="title">Alfabetycznie</option>
                        </select>
                    </div>
                </div>

                <!-- Grid wiadomości -->
                <div class="news-grid" id="newsGrid">
                    <!-- Wiadomości będą ładowane dynamicznie -->
                </div>

                <!-- Paginacja -->
                <div class="pagination" id="pagination">
                    <!-- Paginacja będzie generowana dynamicznie -->
                </div>

                <!-- Stan pustej listy -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <h3>Brak wiadomości</h3>
                    <p>Nie znaleziono wiadomości spełniających kryteria wyszukiwania.</p>
                    <button class="btn btn-primary" onclick="resetFilters()">
                        <i class="fas fa-redo"></i>
                        Resetuj filtry
                    </button>
                </div>

                <!-- Loading state -->
                <div class="loading-state" id="loadingState">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <p>Ładowanie wiadomości...</p>
                </div>
            </section>
        </div>
    </main>



    <!-- Kontener na stopkę - zostanie wypełniony przez templates.js -->
    <div id="footer"></div>

    <!-- Modal szczegółów wiadomości -->
    <div class="modal" id="newsModal">
        <div class="modal-content large">
            <div class="modal-header">
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="newsModalBody">
                <!-- Treść wiadomości będzie ładowana dynamicznie -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/templates.js"></script>
    <script src="js/menu.js"></script>
    <script src="js/wiadomosci.js"></script>
    <script src="js/install-app.js"></script>
</body>
</html>
