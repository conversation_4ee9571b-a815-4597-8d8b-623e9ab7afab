/**
 * Katalog firm - dynamiczne ładowanie z API
 * Żyrardów Poleca
 */

class CompaniesCatalog {
    constructor() {
        this.apiUrl = 'admin/api/companies.php';
        this.topCompaniesContainer = document.getElementById('topCompaniesList');
        this.couponsContainer = document.getElementById('couponsGrid');
        this.offersContainer = document.getElementById('offersGrid');

        this.init();
    }

    async init() {
        try {
            await this.loadTopCompanies();
            await this.loadCoupons();
            await this.loadOffers();
        } catch (error) {
            console.error('Błąd inicjalizacji katalogu firm:', error);
        }
    }

    /**
     * Załaduj TOP 3 firmy
     */
    async loadTopCompanies() {
        try {
            const response = await fetch(`${this.apiUrl}?path=top`);
            const data = await response.json();

            if (data.success && data.data.length > 0) {
                this.renderTopCompanies(data.data);
            } else {
                this.renderEmptyTopCompanies();
            }
        } catch (error) {
            console.error('Błąd ładowania TOP firm:', error);
            this.renderErrorTopCompanies();
        }
    }

    /**
     * Renderuj TOP 3 firmy
     */
    renderTopCompanies(companies) {
        if (!this.topCompaniesContainer) return;

        const html = companies.map((company, index) => `
            <div class="top-company-card" data-company-id="${company.id}">
                <div class="company-badge">TOP ${company.topPosition || index + 1}</div>
                <div class="company-logo">
                    <img src="${company.logo || 'images/default-company-logo.svg'}"
                         alt="Logo ${company.name}"
                         onerror="this.src='images/default-company-logo.svg'">
                </div>
                <div class="company-info">
                    <h4>${this.escapeHtml(company.name)}</h4>
                    <div class="company-tags">
                        <span class="company-tag">${this.escapeHtml(company.category_name || 'Usługi')}</span>
                        ${company.subcategory_name ? `<span class="company-tag">${this.escapeHtml(company.subcategory_name)}</span>` : ''}
                    </div>
                    <p class="company-description">${this.escapeHtml(company.description || 'Profesjonalne usługi w Żyrardowie.')}</p>
                    <div class="company-contact">
                        ${company.address ? `<p><i class="fas fa-map-marker-alt"></i> ${this.escapeHtml(company.address)}</p>` : ''}
                        ${company.phone ? `<p><i class="fas fa-phone"></i> ${this.escapeHtml(company.phone)}</p>` : ''}
                    </div>
                    <div class="company-actions">
                        ${company.website ? `<a href="${this.escapeHtml(company.website)}" target="_blank" class="btn btn-sm btn-outline">Strona WWW</a>` : ''}
                        ${company.phone ? `<a href="tel:${this.escapeHtml(company.phone)}" class="btn btn-sm btn-primary">Zadzwoń</a>` : ''}
                    </div>
                </div>
            </div>
        `).join('');

        this.topCompaniesContainer.innerHTML = html;

        // Dodaj obsługę kliknięć
        this.addCompanyClickHandlers();
    }

    /**
     * Renderuj pustą sekcję TOP firm
     */
    renderEmptyTopCompanies() {
        if (!this.topCompaniesContainer) return;

        this.topCompaniesContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-building"></i>
                <h4>Brak TOP firm</h4>
                <p>Obecnie nie ma żadnych firm w rankingu TOP 3.</p>
            </div>
        `;
    }

    /**
     * Renderuj błąd ładowania TOP firm
     */
    renderErrorTopCompanies() {
        if (!this.topCompaniesContainer) return;

        this.topCompaniesContainer.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>Błąd ładowania</h4>
                <p>Nie udało się załadować listy TOP firm.</p>
                <button class="btn btn-outline btn-sm" onclick="companiesCatalog.loadTopCompanies()">
                    <i class="fas fa-redo"></i> Spróbuj ponownie
                </button>
            </div>
        `;
    }

    /**
     * Załaduj kupony
     */
    async loadCoupons() {
        try {
            const response = await fetch('admin/api/coupons.php?path=active&limit=4');
            const data = await response.json();

            if (data.success && data.data.length > 0) {
                this.renderCoupons(data.data);
            } else {
                this.renderEmptyCoupons();
            }
        } catch (error) {
            console.error('Błąd ładowania kuponów:', error);
            this.renderErrorCoupons();
        }
    }

    /**
     * Renderuj kupony
     */
    renderCoupons(coupons) {
        if (!this.couponsContainer) return;

        const html = coupons.map(coupon => `
            <div class="coupon-card" data-coupon-id="${coupon.id}">
                <div class="coupon-header">
                    <div class="coupon-logo">
                        <img src="${coupon.company_logo || 'images/default-company-logo.svg'}"
                             alt="Logo ${coupon.company_name}"
                             onerror="this.src='images/default-company-logo.svg'">
                    </div>
                    <div class="coupon-discount">${coupon.discount_value}${coupon.discount_type === 'percentage' ? '%' : ' zł'}</div>
                </div>
                <div class="coupon-content">
                    <h3>${this.escapeHtml(coupon.company_name)}</h3>
                    <p class="coupon-title">${this.escapeHtml(coupon.title)}</p>
                    <p class="coupon-description">${this.escapeHtml(coupon.description)}</p>
                    <div class="coupon-validity">
                        <i class="far fa-clock"></i>
                        <span>Ważny do: ${this.formatDate(coupon.validUntil)}</span>
                    </div>
                    <div class="coupon-code-container">
                        <button class="coupon-code-btn" data-code="${coupon.code}">Pokaż kod</button>
                        <div class="coupon-code">${coupon.code}</div>
                    </div>
                </div>
            </div>
        `).join('');

        this.couponsContainer.innerHTML = html;

        // Dodaj obsługę pokazywania kodów kuponów
        this.addCouponCodeHandlers();
    }

    /**
     * Renderuj pustą sekcję kuponów
     */
    renderEmptyCoupons() {
        if (!this.couponsContainer) return;

        this.couponsContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <h4>Brak aktywnych kuponów</h4>
                <p>Obecnie nie ma dostępnych kuponów rabatowych.</p>
            </div>
        `;
    }

    /**
     * Renderuj błąd ładowania kuponów
     */
    renderErrorCoupons() {
        if (!this.couponsContainer) return;

        this.couponsContainer.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>Błąd ładowania kuponów</h4>
                <button class="btn btn-outline btn-sm" onclick="companiesCatalog.loadCoupons()">
                    <i class="fas fa-redo"></i> Spróbuj ponownie
                </button>
            </div>
        `;
    }

    /**
     * Załaduj oferty specjalne
     */
    async loadOffers() {
        try {
            const response = await fetch('admin/api/offers.php?path=active&limit=2');
            const data = await response.json();

            if (data.success && data.data.length > 0) {
                this.renderOffers(data.data);
            } else {
                this.renderEmptyOffers();
            }
        } catch (error) {
            console.error('Błąd ładowania ofert:', error);
            this.renderErrorOffers();
        }
    }

    /**
     * Renderuj oferty specjalne
     */
    renderOffers(offers) {
        if (!this.offersContainer) return;

        const html = offers.map((offer, index) => `
            <div class="offer-card-new ${index === 0 ? 'featured' : ''}" data-offer-id="${offer.id}">
                <div class="offer-badge-new ${offer.badge_type || 'hot'}">${offer.badge_text || '🔥 HOT'}</div>
                <div class="offer-content-new">
                    <div class="offer-company-new">
                        <img src="${offer.company_logo || 'images/default-company-logo.svg'}"
                             alt="Logo ${offer.company_name}"
                             class="company-logo-small"
                             onerror="this.src='images/default-company-logo.svg'">
                        <div class="company-info">
                            <h4>${this.escapeHtml(offer.company_name)}</h4>
                            <span class="company-category">${this.escapeHtml(offer.category_name)}</span>
                        </div>
                    </div>
                    <h3 class="offer-title-new">${this.escapeHtml(offer.title)}</h3>
                    <p class="offer-description-new">${this.escapeHtml(offer.description)}</p>
                    <div class="offer-details">
                        <div class="offer-price-new">
                            ${offer.original_price ? `<span class="price-old">${offer.original_price} zł</span>` : ''}
                            <span class="price-new">${offer.price} zł</span>
                            ${offer.original_price ? `<span class="price-save">Oszczędzasz ${offer.original_price - offer.price} zł</span>` : ''}
                        </div>
                        <div class="offer-validity">
                            <i class="fas fa-clock"></i>
                            <span>Ważne do ${this.formatDate(offer.validUntil)}</span>
                        </div>
                    </div>
                    <div class="offer-actions">
                        <button class="btn btn-primary btn-full js-offer-details" data-offer="${offer.id}">
                            <i class="fas fa-eye"></i> Zobacz szczegóły
                        </button>
                        <a href="#categories-section" class="btn btn-outline btn-full">
                            <i class="fas fa-building"></i> Skontaktuj się z firmą
                        </a>
                    </div>
                </div>
            </div>
        `).join('');

        this.offersContainer.innerHTML = html;

        // Dodaj obsługę szczegółów ofert
        this.addOfferDetailsHandlers();
    }

    /**
     * Renderuj pustą sekcję ofert
     */
    renderEmptyOffers() {
        if (!this.offersContainer) return;

        this.offersContainer.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-gift"></i>
                <h4>Brak aktywnych ofert</h4>
                <p>Obecnie nie ma dostępnych ofert specjalnych.</p>
            </div>
        `;
    }

    /**
     * Renderuj błąd ładowania ofert
     */
    renderErrorOffers() {
        if (!this.offersContainer) return;

        this.offersContainer.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h4>Błąd ładowania ofert</h4>
                <button class="btn btn-outline btn-sm" onclick="companiesCatalog.loadOffers()">
                    <i class="fas fa-redo"></i> Spróbuj ponownie
                </button>
            </div>
        `;
    }

    /**
     * Dodaj obsługę kliknięć na firmy
     */
    addCompanyClickHandlers() {
        const companyCards = document.querySelectorAll('.top-company-card');
        companyCards.forEach(card => {
            card.addEventListener('click', (e) => {
                if (e.target.tagName !== 'A' && e.target.tagName !== 'BUTTON') {
                    const companyId = card.dataset.companyId;
                    this.showCompanyDetails(companyId);
                }
            });
        });
    }

    /**
     * Dodaj obsługę kodów kuponów
     */
    addCouponCodeHandlers() {
        const codeButtons = document.querySelectorAll('.coupon-code-btn');
        codeButtons.forEach(button => {
            button.addEventListener('click', () => {
                const codeDiv = button.nextElementSibling;
                codeDiv.style.display = codeDiv.style.display === 'block' ? 'none' : 'block';
                button.textContent = codeDiv.style.display === 'block' ? 'Ukryj kod' : 'Pokaż kod';
            });
        });
    }

    /**
     * Dodaj obsługę szczegółów ofert
     */
    addOfferDetailsHandlers() {
        const detailButtons = document.querySelectorAll('.js-offer-details');
        detailButtons.forEach(button => {
            button.addEventListener('click', () => {
                const offerId = button.dataset.offer;
                this.showOfferDetails(offerId);
            });
        });
    }

    /**
     * Pokaż szczegóły firmy
     */
    showCompanyDetails(companyId) {
        // Implementacja modalu szczegółów firmy
        console.log('Pokazywanie szczegółów firmy:', companyId);
    }

    /**
     * Pokaż szczegóły oferty
     */
    showOfferDetails(offerId) {
        // Implementacja modalu szczegółów oferty
        console.log('Pokazywanie szczegółów oferty:', offerId);
    }

    /**
     * Escape HTML
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Formatuj datę
     */
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('pl-PL');
    }

    /**
     * Pokaż powiadomienie
     */
    showNotification(message, type = 'info') {
        // Utwórz element powiadomienia
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        // Dodaj animację wejścia
        notification.style.transform = 'translateX(100%)';

        // Dodaj do strony
        document.body.appendChild(notification);

        // Animacja wejścia
        setTimeout(() => {
            notification.classList.add('show');
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Usuń po 3 sekundach
        setTimeout(() => {
            notification.classList.add('hiding');
            notification.style.transform = 'translateX(100%)';

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }
}

// Inicjalizacja po załadowaniu DOM
document.addEventListener('DOMContentLoaded', () => {
    window.companiesCatalog = new CompaniesCatalog();
});
